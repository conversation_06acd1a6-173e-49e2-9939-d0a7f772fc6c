# Frontend - WEBBANHANG

Giao diện quản lý sản phẩm được xây dựng với jQuery, Bootstrap 5 và tích hợp hoàn toàn với RESTful API.

## 🚀 Demo

Mở file `demo.html` để xem trang giới thiệu và truy cập `index.html` để sử dụng ứng dụng.

**URL Demo:** `http://localhost/WEBBANHANG/frontend/demo.html`
**URL Ứng dụng:** `http://localhost/WEBBANHANG/frontend/index.html`

## 📁 Cấu trúc thư mục

```
frontend/
├── index.html          # Trang chính của ứng dụng
├── demo.html           # Trang demo/giới thiệu
├── css/
│   └── style.css       # Custom CSS styles
├── js/
│   └── app.js          # JavaScript chính
└── README.md           # Tài liệu này
```

## ✨ Tính năng

### 🛍️ Quản lý sản phẩm
- ✅ Xem danh sách sản phẩm với phân trang
- ✅ Thêm sản phẩm mới
- ✅ Chỉnh sửa sản phẩm
- ✅ Xóa sản phẩm (có xác nhận)
- ✅ Xem chi tiết sản phẩm

### 🔍 Tìm kiếm & Lọc
- ✅ Tìm kiếm theo tên sản phẩm
- ✅ Tìm kiếm theo mô tả
- ✅ Lọc theo danh mục
- ✅ Sắp xếp theo tên, giá, ID

### 🎨 Giao diện
- ✅ Responsive design (mobile-friendly)
- ✅ Bootstrap 5 components
- ✅ Font Awesome icons
- ✅ Smooth animations
- ✅ Loading indicators
- ✅ Toast notifications

### 🔧 Tính năng kỹ thuật
- ✅ Real-time form validation
- ✅ Error handling
- ✅ AJAX requests với jQuery
- ✅ Debounced search
- ✅ Pagination
- ✅ Modal dialogs

## 🛠️ Công nghệ sử dụng

- **jQuery 3.6.0** - DOM manipulation và AJAX
- **Bootstrap 5.1.3** - UI framework
- **Font Awesome 6.0** - Icons
- **CSS3** - Custom styling với animations
- **HTML5** - Semantic markup

## 📋 Yêu cầu

1. **Web Server** (Apache/Nginx) đang chạy
2. **Backend API** đã được cấu hình và hoạt động
3. **Database** MySQL với dữ liệu mẫu
4. **Browser** hỗ trợ ES6+ (Chrome, Firefox, Safari, Edge)

## 🚀 Cách sử dụng

### 1. Khởi động server
```bash
# Đảm bảo Laragon đang chạy
# Hoặc khởi động Apache/MySQL thủ công
```

### 2. Truy cập ứng dụng
```
http://localhost/WEBBANHANG/frontend/index.html
```

### 3. Các thao tác cơ bản

#### Thêm sản phẩm mới
1. Click nút "Thêm sản phẩm"
2. Điền thông tin vào form
3. Click "Lưu"

#### Chỉnh sửa sản phẩm
1. Click icon "Edit" (✏️) ở cột thao tác
2. Cập nhật thông tin
3. Click "Lưu"

#### Xóa sản phẩm
1. Click icon "Delete" (🗑️) ở cột thao tác
2. Xác nhận xóa

#### Tìm kiếm
1. Nhập từ khóa vào ô "Tìm kiếm sản phẩm"
2. Kết quả hiển thị real-time

#### Lọc theo danh mục
1. Chọn danh mục từ dropdown "Tất cả danh mục"
2. Danh sách sẽ được lọc tự động

## 🔧 Cấu hình

### API Base URL
Trong file `js/app.js`, cấu hình URL API:
```javascript
const API_BASE_URL = '../api';
```

### Pagination
Số sản phẩm hiển thị mỗi trang:
```javascript
const itemsPerPage = 10;
```

## 🐛 Xử lý lỗi

### Lỗi kết nối API
- Kiểm tra server backend đang chạy
- Kiểm tra URL API trong `js/app.js`
- Xem Console để debug

### Lỗi validation
- Form sẽ hiển thị lỗi real-time
- Các trường bắt buộc được đánh dấu

### Lỗi CORS
- Đảm bảo frontend và backend cùng domain
- Hoặc cấu hình CORS headers trong backend

## 📱 Responsive Design

Ứng dụng được tối ưu cho:
- **Desktop** (≥1200px)
- **Tablet** (768px - 1199px)  
- **Mobile** (<768px)

## 🎯 API Endpoints sử dụng

- `GET /api/product` - Lấy danh sách sản phẩm
- `GET /api/product/{id}` - Lấy chi tiết sản phẩm
- `POST /api/product` - Thêm sản phẩm mới
- `PUT /api/product/{id}` - Cập nhật sản phẩm
- `DELETE /api/product/{id}` - Xóa sản phẩm
- `GET /api/category` - Lấy danh sách danh mục

## 🔍 Debug

### Bật Console Log
Mở Developer Tools (F12) để xem:
- AJAX requests/responses
- JavaScript errors
- Network issues

### Test API trực tiếp
Sử dụng file `test_api_endpoints.html` để test API độc lập.

## 📈 Performance

- **Debounced search** (300ms delay)
- **Lazy loading** cho pagination
- **Optimized DOM manipulation**
- **Minimal HTTP requests**

## 🔮 Tính năng tương lai

- [ ] Upload hình ảnh sản phẩm
- [ ] Bulk operations (xóa nhiều)
- [ ] Export/Import CSV
- [ ] Advanced filters
- [ ] Real-time notifications
- [ ] Dark mode
- [ ] PWA support

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra Console errors
2. Xác nhận API hoạt động
3. Kiểm tra network requests
4. Đọc documentation API
