    <!-- Simple footer for login -->
    <footer class="login-footer">
        <div class="container text-center">
            <div class="row">
                <div class="col-12">
                    <p class="mb-2 text-muted">
                        &copy; 2024 Web Bán Hàng. Hệ thống quản lý hiện đại.
                    </p>
                    <div class="d-flex justify-content-center gap-3 mb-3">
                        <span class="badge bg-primary">
                            <i class="fas fa-shield-alt me-1"></i>JWT Auth
                        </span>
                        <span class="badge bg-success">
                            <i class="fas fa-api me-1"></i>REST API
                        </span>
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-mobile-alt me-1"></i>Responsive
                        </span>
                    </div>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="text-muted text-decoration-none">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .login-footer {
            background: white;
            padding: 2rem 0;
            margin-top: auto;
            border-top: 1px solid var(--border-color);
        }

        .login-footer .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .login-footer a {
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .login-footer a:hover {
            color: var(--primary-color) !important;
            transform: translateY(-2px);
        }

        /* Make body flex to push footer down */
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .login-container {
            flex: 1;
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Enhanced Toast function
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();
            
            const iconMap = {
                'success': 'fas fa-check-circle',
                'danger': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            
            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast align-items-center text-white bg-${type} border-0 shadow-lg`;
            toast.style.borderRadius = '0.75rem';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="${iconMap[type] || 'fas fa-info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 4000
            });
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }
    </script>
</body>
</html>
