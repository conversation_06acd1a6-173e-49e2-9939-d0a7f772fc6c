<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final JWT Authentication Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🎯 Final JWT Authentication Test</h1>
        <p class="lead">Test hoàn chỉnh JWT authentication cho cả Product và Category API</p>
        
        <!-- Login Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Authentication</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-2" onclick="loginAdmin()">Login as Admin</button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="loginUser()">Login as User</button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-outline-danger w-100 mb-2" onclick="clearToken()">Clear Token</button>
                        <button class="btn btn-outline-info w-100 mb-2" onclick="showTokenInfo()">Show Token Info</button>
                    </div>
                </div>
                <div id="authResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Test Matrix -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Complete Test Matrix</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>API Endpoint</th>
                                <th>Method</th>
                                <th>Auth Required</th>
                                <th>Test Action</th>
                                <th>Result</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Product API -->
                            <tr>
                                <td>/api/product</td>
                                <td>GET</td>
                                <td>❌ No</td>
                                <td><button class="btn btn-sm btn-success" onclick="testAPI('product', 'GET', '', false)">Test</button></td>
                                <td id="result-product-get"></td>
                            </tr>
                            <tr>
                                <td>/api/product</td>
                                <td>POST</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-warning" onclick="testAPI('product', 'POST', '', true)">Test</button></td>
                                <td id="result-product-post"></td>
                            </tr>
                            <tr>
                                <td>/api/product/1</td>
                                <td>PUT</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('product', 'PUT', '/1', true)">Test</button></td>
                                <td id="result-product-put"></td>
                            </tr>
                            <tr>
                                <td>/api/product/999</td>
                                <td>DELETE</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-danger" onclick="testAPI('product', 'DELETE', '/999', true)">Test</button></td>
                                <td id="result-product-delete"></td>
                            </tr>
                            <!-- Category API -->
                            <tr>
                                <td>/api/category</td>
                                <td>GET</td>
                                <td>❌ No</td>
                                <td><button class="btn btn-sm btn-success" onclick="testAPI('category', 'GET', '', false)">Test</button></td>
                                <td id="result-category-get"></td>
                            </tr>
                            <tr>
                                <td>/api/category</td>
                                <td>POST</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-warning" onclick="testAPI('category', 'POST', '', true)">Test</button></td>
                                <td id="result-category-post"></td>
                            </tr>
                            <tr>
                                <td>/api/category/1</td>
                                <td>PUT</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-info" onclick="testAPI('category', 'PUT', '/1', true)">Test</button></td>
                                <td id="result-category-put"></td>
                            </tr>
                            <tr>
                                <td>/api/category/999</td>
                                <td>DELETE</td>
                                <td>✅ JWT + Admin</td>
                                <td><button class="btn btn-sm btn-danger" onclick="testAPI('category', 'DELETE', '/999', true)">Test</button></td>
                                <td id="result-category-delete"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="runAllTests()">🚀 Run All Tests</button>
                    <button class="btn btn-outline-secondary" onclick="clearAllResults()">Clear Results</button>
                </div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="card">
            <div class="card-header">
                <h5>📋 Expected Results</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">✅ Should Work (Admin Token)</h6>
                        <ul>
                            <li>GET requests: 200 OK (no token needed)</li>
                            <li>POST/PUT/DELETE with admin token: 201/200 OK</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">❌ Should Fail</h6>
                        <ul>
                            <li>POST/PUT/DELETE without token: 401 Unauthorized</li>
                            <li>POST/PUT/DELETE with user token: 403 Forbidden</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentToken = '';
        let currentRole = '';

        async function loginAdmin() {
            await login('admin', 'admin123');
        }

        async function loginUser() {
            await login('user', 'admin123');
        }

        async function login(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    currentToken = data.token;
                    currentRole = username; // Simple role tracking
                    
                    document.getElementById('authResult').innerHTML = 
                        `<div class="alert alert-success">
                            ✅ Logged in as <strong>${username}</strong><br>
                            Token: <code>${data.token.substring(0, 30)}...</code>
                        </div>`;
                } else {
                    document.getElementById('authResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed for ${username}</div>`;
                }
            } catch (error) {
                document.getElementById('authResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        function clearToken() {
            currentToken = '';
            currentRole = '';
            document.getElementById('authResult').innerHTML = 
                '<div class="alert alert-secondary">Token cleared</div>';
        }

        function showTokenInfo() {
            if (!currentToken) {
                document.getElementById('authResult').innerHTML = 
                    '<div class="alert alert-warning">No token available</div>';
                return;
            }

            try {
                const parts = currentToken.split('.');
                const payload = JSON.parse(atob(parts[1]));
                
                document.getElementById('authResult').innerHTML = 
                    `<div class="alert alert-info">
                        <h6>Token Info:</h6>
                        <strong>User:</strong> ${payload.data?.username}<br>
                        <strong>Role:</strong> ${payload.data?.role}<br>
                        <strong>Expires:</strong> ${new Date(payload.exp * 1000).toLocaleString()}<br>
                        <strong>Valid:</strong> ${payload.exp > Math.floor(Date.now() / 1000) ? '✅ Yes' : '❌ Expired'}
                    </div>`;
            } catch (error) {
                document.getElementById('authResult').innerHTML = 
                    `<div class="alert alert-danger">Error parsing token: ${error.message}</div>`;
            }
        }

        async function testAPI(type, method, path, requiresAuth) {
            const resultId = `result-${type}-${method.toLowerCase()}`;
            const resultCell = document.getElementById(resultId);
            
            try {
                const headers = { 'Content-Type': 'application/json' };
                
                if (requiresAuth && currentToken) {
                    headers['Authorization'] = `Bearer ${currentToken}`;
                }

                let body = null;
                if (method === 'POST') {
                    body = JSON.stringify({
                        name: `Test ${type} ${Date.now()}`,
                        description: `Created via final test`,
                        ...(type === 'product' && { price: 100, category_id: 2 })
                    });
                } else if (method === 'PUT') {
                    body = JSON.stringify({
                        name: `Updated ${type} ${Date.now()}`,
                        description: `Updated via final test`,
                        ...(type === 'product' && { price: 200, category_id: 2 })
                    });
                }

                const response = await fetch(`/webbanhang/api/${type}${path}`, {
                    method,
                    headers,
                    body
                });

                const data = await response.json();
                const statusClass = response.status >= 200 && response.status < 300 ? 'success' : 'danger';
                
                resultCell.innerHTML = 
                    `<span class="badge bg-${statusClass}">${response.status}</span>`;
                    
            } catch (error) {
                resultCell.innerHTML = 
                    `<span class="badge bg-danger">ERROR</span>`;
            }
        }

        async function runAllTests() {
            const tests = [
                ['product', 'GET', '', false],
                ['product', 'POST', '', true],
                ['product', 'PUT', '/1', true],
                ['product', 'DELETE', '/999', true],
                ['category', 'GET', '', false],
                ['category', 'POST', '', true],
                ['category', 'PUT', '/1', true],
                ['category', 'DELETE', '/999', true]
            ];

            for (const [type, method, path, requiresAuth] of tests) {
                await testAPI(type, method, path, requiresAuth);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            }
        }

        function clearAllResults() {
            const resultCells = document.querySelectorAll('[id^="result-"]');
            resultCells.forEach(cell => {
                cell.innerHTML = '<span class="text-muted">-</span>';
            });
        }

        // Initialize
        clearAllResults();
    </script>
</body>
</html>
