<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Frontend - WEBBANHANG</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .demo-container {
            padding: 50px 0;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
        }
        
        .demo-title {
            color: #333;
            margin-bottom: 30px;
            font-weight: 700;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        
        .feature-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            color: #28a745;
            margin-right: 10px;
            width: 20px;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s ease;
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
            color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .tech-stack {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        
        .tech-badge {
            display: inline-block;
            background: #f8f9fa;
            color: #495057;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .api-status {
            background: #e8f5e9;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #4caf50;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="demo-card">
                        <h1 class="demo-title">
                            <i class="fas fa-rocket"></i>
                            Frontend Demo - WEBBANHANG
                        </h1>
                        
                        <p class="demo-description">
                            Giao diện quản lý sản phẩm được xây dựng với jQuery, Bootstrap 5 và tích hợp hoàn toàn với RESTful API.
                            Trải nghiệm giao diện hiện đại, responsive và thân thiện với người dùng.
                        </p>
                        
                        <div class="api-status">
                            <h6><span class="status-indicator"></span>API Status: <span class="text-success">Online</span></h6>
                            <small class="text-muted">Kết nối thành công với backend API</small>
                        </div>
                        
                        <div class="feature-list">
                            <h5><i class="fas fa-star"></i> Tính năng chính:</h5>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Quản lý sản phẩm:</strong> Thêm, sửa, xóa, xem chi tiết sản phẩm
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Tìm kiếm & Lọc:</strong> Tìm kiếm theo tên, lọc theo danh mục
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Sắp xếp:</strong> Sắp xếp theo tên, giá, ID
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Phân trang:</strong> Hiển thị dữ liệu với phân trang thông minh
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Validation:</strong> Kiểm tra dữ liệu đầu vào real-time
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>Responsive:</strong> Tương thích mọi thiết bị
                            </div>
                            
                            <div class="feature-item">
                                <i class="fas fa-check feature-icon"></i>
                                <strong>UX/UI:</strong> Giao diện hiện đại với animations mượt mà
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="index.html" class="btn-demo">
                                <i class="fas fa-play"></i> Trải nghiệm ngay
                            </a>
                        </div>
                        
                        <div class="tech-stack">
                            <h6><i class="fas fa-code"></i> Công nghệ sử dụng:</h6>
                            <span class="tech-badge"><i class="fab fa-js-square"></i> jQuery 3.6.0</span>
                            <span class="tech-badge"><i class="fab fa-bootstrap"></i> Bootstrap 5.1.3</span>
                            <span class="tech-badge"><i class="fas fa-icons"></i> Font Awesome 6.0</span>
                            <span class="tech-badge"><i class="fas fa-mobile-alt"></i> Responsive Design</span>
                            <span class="tech-badge"><i class="fas fa-server"></i> RESTful API</span>
                            <span class="tech-badge"><i class="fas fa-database"></i> MySQL</span>
                            <span class="tech-badge"><i class="fab fa-php"></i> PHP</span>
                        </div>
                        
                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Đảm bảo server đang chạy và database đã được cấu hình đúng
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Test API connection
            $.ajax({
                url: '../api/product',
                method: 'GET',
                timeout: 5000
            })
            .done(function() {
                $('.api-status').removeClass('alert-warning').addClass('alert-success');
                $('.api-status .text-warning').removeClass('text-warning').addClass('text-success').text('Online');
                $('.api-status small').text('Kết nối thành công với backend API');
            })
            .fail(function() {
                $('.api-status').removeClass('alert-success').addClass('alert-warning');
                $('.api-status .text-success').removeClass('text-success').addClass('text-warning').text('Offline');
                $('.api-status small').text('Không thể kết nối với backend API. Vui lòng kiểm tra server.');
                $('.status-indicator').css('background', '#ff9800');
            });
        });
    </script>
</body>
</html>
