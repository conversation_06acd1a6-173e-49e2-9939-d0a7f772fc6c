</div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <!-- Footer -->
    <footer class="footer mt-5" style="background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%); color: white;">
        <div class="container py-5">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="mb-3 fw-bold">
                        <i class="fas fa-store me-2" style="color: var(--warning-color);"></i>
                        Web Bán Hàng
                    </h5>
                    <p class="text-light opacity-75 mb-4">
                        Hệ thống quản lý bán hàng hiện đại với JWT authentication và API RESTful
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light opacity-75 fs-5 hover-effect">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-light opacity-75 fs-5 hover-effect">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-light opacity-75 fs-5 hover-effect">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="text-light opacity-75 fs-5 hover-effect">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2">
                    <h6 class="text-uppercase mb-3 fw-semibold" style="color: var(--warning-color);">Quản lý</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="/webbanhang/Product" class="text-light opacity-75 text-decoration-none hover-link">
                                <i class="fas fa-box me-2"></i>Sản phẩm
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="/webbanhang/Category" class="text-light opacity-75 text-decoration-none hover-link">
                                <i class="fas fa-tags me-2"></i>Danh mục
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h6 class="text-uppercase mb-3 fw-semibold" style="color: var(--warning-color);">API & Tools</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="/webbanhang/final_jwt_test.html" class="text-light opacity-75 text-decoration-none hover-link">
                                <i class="fas fa-flask me-2"></i>API Test Tool
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="/webbanhang/jwt_test.php" class="text-light opacity-75 text-decoration-none hover-link">
                                <i class="fas fa-key me-2"></i>JWT Test
                            </a>
                        </li>
                        <li class="mb-2">
                            <span class="text-light opacity-75">
                                <i class="fas fa-shield-alt me-2"></i>JWT Authentication
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h6 class="text-uppercase mb-3 fw-semibold" style="color: var(--warning-color);">Thông tin</h6>
                    <div class="text-light opacity-75">
                        <p class="mb-2">
                            <i class="fas fa-code me-2"></i>
                            Built with PHP & Bootstrap 5
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-database me-2"></i>
                            MySQL Database
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-lock me-2"></i>
                            JWT Security
                        </p>
                    </div>
                </div>
            </div>

            <hr class="my-4 opacity-25">

            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0 text-light opacity-75">
                        &copy; 2024 Web Bán Hàng. Powered by modern web technologies.
                    </p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <div class="d-flex justify-content-center justify-content-md-end gap-3">
                        <span class="badge bg-primary">
                            <i class="fas fa-shield-alt me-1"></i>JWT Auth
                        </span>
                        <span class="badge bg-success">
                            <i class="fas fa-api me-1"></i>REST API
                        </span>
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-mobile-alt me-1"></i>Responsive
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .hover-effect {
            transition: all 0.3s ease;
        }

        .hover-effect:hover {
            color: var(--warning-color) !important;
            transform: translateY(-2px);
        }

        .hover-link {
            transition: all 0.3s ease;
        }

        .hover-link:hover {
            color: white !important;
            transform: translateX(5px);
        }

        .footer {
            margin-top: auto;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
    </style>

    <script>
        // Enhanced Toast function with better styling
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();

            const iconMap = {
                'success': 'fas fa-check-circle',
                'danger': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast align-items-center text-white bg-${type} border-0 shadow-lg`;
            toast.style.borderRadius = '0.75rem';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="${iconMap[type] || 'fas fa-info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 4000
            });
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to main content
            const mainContent = document.querySelector('.container-main');
            if (mainContent) {
                mainContent.style.opacity = '0';
                mainContent.style.transform = 'translateY(20px)';
                mainContent.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    mainContent.style.opacity = '1';
                    mainContent.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    </script>
</body>
</html>