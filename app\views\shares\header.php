
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Bán <PERSON> - <PERSON><PERSON><PERSON>n lý hiện đ<PERSON>i</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1e293b;
            --light-color: #f1f5f9;
            --border-color: #e2e8f0;
            --text-muted: #64748b;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark-color);
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
            box-shadow: var(--shadow-lg);
            border: none;
            padding: 1rem 0;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1030;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-right: 0.75rem;
            font-size: 1.75rem;
        }

        .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9) !important;
            transition: all 0.3s ease;
            border-radius: 0.75rem;
            margin: 0 0.25rem;
            padding: 0.75rem 1.25rem !important;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white !important;
        }

        .badge {
            font-size: 0.7rem;
            padding: 0.35rem 0.6rem;
            border-radius: 1rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-lg);
            border-radius: 1rem;
            padding: 0.75rem;
            margin-top: 0.5rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 9999 !important;
            position: absolute !important;
        }

        .dropdown-item {
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            margin-bottom: 0.25rem;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--light-color), #e2e8f0);
            color: var(--primary-color);
            transform: translateX(4px);
        }

        .dropdown-item.logout-item:hover {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: var(--danger-color);
        }

        .dropdown-header {
            font-weight: 600;
            color: var(--text-muted);
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Logout confirmation modal styles */
        .logout-modal .modal-content {
            border: none;
            border-radius: 1rem;
            box-shadow: var(--shadow-lg);
        }

        .logout-modal .modal-header {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
            border-radius: 1rem 1rem 0 0;
            border: none;
        }

        .logout-modal .btn-logout {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .logout-modal .btn-logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .logout-modal .btn-cancel {
            background: var(--light-color);
            border: 1px solid var(--border-color);
            color: var(--dark-color);
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .logout-modal .btn-cancel:hover {
            background: white;
            transform: translateY(-1px);
        }

        .container-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .product-image {
            max-width: 100px;
            height: auto;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
        }

        /* Animation cho loading */
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.25rem;
            }

            .nav-link {
                padding: 0.5rem 1rem !important;
                margin: 0.25rem 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/webbanhang">
                <i class="fas fa-store"></i>
                <span>Web Bán Hàng</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/webbanhang/Product">
                            <i class="fas fa-box me-2"></i>Sản phẩm
                        </a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/webbanhang/Product/add">
                            <i class="fas fa-plus-circle me-2"></i>Thêm sản phẩm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/webbanhang/Category">
                            <i class="fas fa-tags me-2"></i>Danh mục
                        </a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/webbanhang/Category/add">
                            <i class="fas fa-plus-circle me-2"></i>Thêm danh mục
                        </a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="/webbanhang/final_jwt_test.html">
                            <i class="fas fa-flask me-2"></i>API Test
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item" id="nav-login">
                        <a class="nav-link" href="/webbanhang/account/login">
                            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                        </a>
                    </li>
                    <li class="nav-item dropdown" id="nav-user" style="display: none;">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <span id="username-display">User</span>
                            <span id="role-badge" class="badge bg-warning text-dark">
                                <i class="fas fa-crown me-1"></i>Admin
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-user me-2"></i>Tài khoản
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="showProfile()">
                                    <i class="fas fa-user-cog me-2"></i>Thông tin cá nhân
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="showSettings()">
                                    <i class="fas fa-cog me-2"></i>Cài đặt
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item logout-item" href="#" onclick="confirmLogout(event)">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    <span class="logout-text">Đăng xuất</span>
                                    <span class="logout-loading d-none">
                                        <i class="fas fa-spinner fa-spin me-2"></i>Đang đăng xuất...
                                    </span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Logout Confirmation Modal -->
    <div class="modal fade logout-modal" id="logoutModal" tabindex="-1" aria-labelledby="logoutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logoutModalLabel">
                        <i class="fas fa-sign-out-alt me-2"></i>Xác nhận đăng xuất
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-question-circle" style="font-size: 3rem; color: var(--warning-color);"></i>
                    </div>
                    <h6 class="mb-3">Bạn có chắc chắn muốn đăng xuất?</h6>
                    <p class="text-muted mb-0">Bạn sẽ cần đăng nhập lại để tiếp tục sử dụng hệ thống.</p>
                </div>
                <div class="modal-footer justify-content-center border-0 pb-4">
                    <button type="button" class="btn btn-cancel me-3" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Hủy
                    </button>
                    <button type="button" class="btn btn-logout" onclick="performLogout()">
                        <span class="logout-btn-text">
                            <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                        </span>
                        <span class="logout-btn-loading d-none">
                            <i class="fas fa-spinner fa-spin me-2"></i>Đang đăng xuất...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Show logout confirmation modal
        function confirmLogout(event) {
            event.preventDefault();
            const logoutModal = new bootstrap.Modal(document.getElementById('logoutModal'));
            logoutModal.show();
        }

        // Perform actual logout
        function performLogout() {
            const logoutBtn = document.querySelector('.btn-logout');
            const btnText = logoutBtn.querySelector('.logout-btn-text');
            const btnLoading = logoutBtn.querySelector('.logout-btn-loading');

            // Show loading state
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            logoutBtn.disabled = true;

            // Clear JWT token
            localStorage.removeItem('jwtToken');

            // Show success message
            showToast('Đăng xuất thành công!', 'success');

            // Smooth redirect after delay
            setTimeout(() => {
                window.location.href = '/webbanhang/account/logout';
            }, 1000);
        }

        // Placeholder functions for profile and settings
        function showProfile() {
            showToast('Tính năng thông tin cá nhân đang được phát triển', 'info');
        }

        function showSettings() {
            showToast('Tính năng cài đặt đang được phát triển', 'info');
        }

        // Function để decode JWT token
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Function kiểm tra user có phải admin không
        function isAdmin() {
            const token = localStorage.getItem('jwtToken');
            if (!token) return false;

            const decoded = decodeJWT(token);
            return decoded && decoded.data && decoded.data.role === 'admin';
        }

        // Function để lấy thông tin user
        function getUserInfo() {
            const token = localStorage.getItem('jwtToken');
            if (!token) return null;

            const decoded = decodeJWT(token);
            return decoded && decoded.data ? decoded.data : null;
        }

        // Function để highlight active nav
        function setActiveNav() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentPath ||
                    (currentPath.includes('/Product') && link.getAttribute('href').includes('/Product')) ||
                    (currentPath.includes('/Category') && link.getAttribute('href').includes('/Category'))) {
                    link.classList.add('active');
                }
            });
        }

        document.addEventListener("DOMContentLoaded", function() {
            const token = localStorage.getItem('jwtToken');
            const navLogin = document.getElementById('nav-login');
            const navUser = document.getElementById('nav-user');
            const adminOnlyItems = document.querySelectorAll('.admin-only');
            const usernameDisplay = document.getElementById('username-display');
            const roleBadge = document.getElementById('role-badge');

            if (token) {
                const userInfo = getUserInfo();

                if (userInfo) {
                    // Hiển thị thông tin user
                    navLogin.style.display = 'none';
                    navUser.style.display = 'block';
                    usernameDisplay.textContent = userInfo.username;

                    // Cập nhật badge role
                    if (userInfo.role === 'admin') {
                        roleBadge.innerHTML = '<i class="fas fa-crown me-1"></i>Admin';
                        roleBadge.className = 'badge bg-warning text-dark';

                        // Hiển thị menu admin
                        adminOnlyItems.forEach(item => {
                            item.style.display = 'block';
                        });
                    } else {
                        roleBadge.innerHTML = '<i class="fas fa-user me-1"></i>User';
                        roleBadge.className = 'badge bg-info text-white';

                        // Ẩn menu admin
                        adminOnlyItems.forEach(item => {
                            item.style.display = 'none';
                        });
                    }
                } else {
                    // Token không hợp lệ
                    localStorage.removeItem('jwtToken');
                    navLogin.style.display = 'block';
                    navUser.style.display = 'none';
                }
            } else {
                navLogin.style.display = 'block';
                navUser.style.display = 'none';

                // Ẩn tất cả menu admin
                adminOnlyItems.forEach(item => {
                    item.style.display = 'none';
                });

                // Redirect to login if accessing protected routes
                const protectedRoutes = ['/webbanhang/Product/add', '/webbanhang/Category/add'];
                if (protectedRoutes.includes(window.location.pathname)) {
                    window.location.href = '/webbanhang/account/login';
                }
            }

            // Set active navigation
            setActiveNav();
        });

        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>

    <div class="container-main">