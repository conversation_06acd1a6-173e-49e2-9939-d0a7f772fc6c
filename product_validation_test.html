<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Validation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Product Validation Test</h1>
        <p class="lead">Test các thay đổi về validation và edit product</p>
        
        <!-- Quick Login -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Quick Login</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="quickLogin('admin', 'admin123')">Login as Admin</button>
                <div id="loginResult" class="mt-2"></div>
            </div>
        </div>

        <!-- Test Cases -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Test Cases</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>✅ Should Work (Relaxed Validation)</h6>
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProductNoDescription()">Create Product (No Description)</button>
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProductZeroPrice()">Create Product (Price = 0)</button>
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProductNegativePrice()">Create Product (Negative Price)</button>
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProductMinimal()">Create Product (Minimal Data)</button>
                    </div>
                    <div class="col-md-6">
                        <h6>❌ Should Fail (Still Required)</h6>
                        <button class="btn btn-danger w-100 mb-2" onclick="testCreateProductNoName()">Create Product (No Name)</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="testCreateProductNoCategory()">Create Product (No Category)</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="testCreateProductInvalidPrice()">Create Product (Invalid Price)</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Edit Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>✏️ Edit Product Test</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-info w-100 mb-2" onclick="testGetProduct()">Get Product Data</button>
                        <button class="btn btn-warning w-100 mb-2" onclick="testUpdateProduct()">Update Product</button>
                    </div>
                    <div class="col-md-6">
                        <a href="/webbanhang/Product" class="btn btn-outline-primary w-100 mb-2" target="_blank">Product List (Web)</a>
                        <a href="/webbanhang/Product/edit/1" class="btn btn-outline-warning w-100 mb-2" target="_blank">Edit Product 1 (Web)</a>
                    </div>
                </div>
                <div id="editResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="card">
            <div class="card-header">
                <h5>📋 Expected Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6>✅ Should Work:</h6>
                    <ul class="mb-0">
                        <li>Create product without description (empty description allowed)</li>
                        <li>Create product with price = 0 (zero price allowed)</li>
                        <li>Create product with negative price (negative price now allowed)</li>
                        <li>Edit product page should load categories properly with dropdown</li>
                        <li>Update product should work with JWT token</li>
                    </ul>
                </div>
                <div class="alert alert-danger">
                    <h6>❌ Should Fail:</h6>
                    <ul class="mb-0">
                        <li>Create product without name (name still required)</li>
                        <li>Create product without category (category still required)</li>
                        <li>Create product with invalid price like "abc" (must be numeric)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Quick login
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ Logged in as ${username}</div>`;
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test functions
        async function testCreateProductNoDescription() {
            await testCreateProduct({
                name: 'Product No Description',
                description: '', // Empty description
                price: 50000,
                category_id: 1
            }, 'No Description');
        }

        async function testCreateProductZeroPrice() {
            await testCreateProduct({
                name: 'Free Product',
                description: 'This is a free product',
                price: 0, // Zero price
                category_id: 1
            }, 'Zero Price');
        }

        async function testCreateProductMinimal() {
            await testCreateProduct({
                name: 'Minimal Product',
                description: '',
                price: 0,
                category_id: 1
            }, 'Minimal Data');
        }

        async function testCreateProductNoName() {
            await testCreateProduct({
                name: '', // Empty name
                description: 'Product without name',
                price: 10000,
                category_id: 1
            }, 'No Name');
        }

        async function testCreateProductNoCategory() {
            await testCreateProduct({
                name: 'Product No Category',
                description: 'Product without category',
                price: 10000,
                category_id: null // No category
            }, 'No Category');
        }

        async function testCreateProductNegativePrice() {
            await testCreateProduct({
                name: 'Negative Price Product',
                description: 'Product with negative price',
                price: -1000, // Negative price (now allowed)
                category_id: 1
            }, 'Negative Price');
        }

        async function testCreateProductInvalidPrice() {
            await testCreateProduct({
                name: 'Invalid Price Product',
                description: 'Product with invalid price',
                price: 'abc', // Invalid price (not numeric)
                category_id: 1
            }, 'Invalid Price');
        }

        async function testCreateProduct(productData, testName) {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(productData)
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>Test: ${testName}</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('testResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        async function testGetProduct() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product/1', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('editResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>GET Product 1</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data, null, 2)}
                    </div>`;
            } catch (error) {
                document.getElementById('editResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        async function testUpdateProduct() {
            const token = localStorage.getItem('jwtToken');
            try {
                const response = await fetch('/webbanhang/api/product/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        name: 'Updated Product Name',
                        description: '', // Empty description should work
                        price: -500, // Negative price should work now
                        category_id: 1
                    })
                });

                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                document.getElementById('editResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>UPDATE Product 1</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data)}
                    </div>`;
            } catch (error) {
                document.getElementById('editResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
