<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Showcase - <PERSON> <PERSON><PERSON>ng</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1e293b;
            --light-color: #f1f5f9;
            --border-color: #e2e8f0;
            --text-muted: #64748b;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark-color);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .demo-button {
            background: linear-gradient(135deg, var(--accent-color), #059669);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            color: white;
        }

        .screenshot-card {
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .screenshot-img {
            width: 100%;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .tech-badge {
            background: var(--light-color);
            color: var(--dark-color);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 500;
            margin: 0.25rem;
            display: inline-block;
            border: 1px solid var(--border-color);
        }

        .stats-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stats-label {
            color: var(--text-muted);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <div class="mb-4">
                    <i class="fas fa-store" style="font-size: 4rem; color: var(--warning-color);"></i>
                </div>
                <h1 class="display-4 fw-bold mb-4">Web Bán Hàng</h1>
                <p class="lead mb-5">
                    Hệ thống quản lý bán hàng hiện đại với JWT Authentication, RESTful API và giao diện responsive
                </p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="/webbanhang/account/login" class="demo-button">
                        <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập Demo
                    </a>
                    <a href="/webbanhang/final_jwt_test.html" class="demo-button">
                        <i class="fas fa-flask me-2"></i>Test API
                    </a>
                    <a href="#features" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-down me-2"></i>Xem tính năng
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Tính năng nổi bật</h2>
                <p class="lead text-muted">Được xây dựng với công nghệ hiện đại và best practices</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3">JWT Authentication</h5>
                        <p class="text-muted">Bảo mật cao với JSON Web Token, phân quyền admin/user rõ ràng</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-api"></i>
                        </div>
                        <h5 class="fw-bold mb-3">RESTful API</h5>
                        <p class="text-muted">API chuẩn REST với CRUD operations đầy đủ cho Product và Category</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Responsive Design</h5>
                        <p class="text-muted">Giao diện đẹp, hiện đại và tương thích mọi thiết bị</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h5 class="fw-bold mb-3">MySQL Database</h5>
                        <p class="text-muted">Cơ sở dữ liệu ổn định với quan hệ chuẩn hóa</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h5 class="fw-bold mb-3">MVC Architecture</h5>
                        <p class="text-muted">Kiến trúc MVC rõ ràng, dễ bảo trì và mở rộng</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h5 class="fw-bold mb-3">API Testing</h5>
                        <p class="text-muted">Built-in testing tools để kiểm tra API endpoints</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tech Stack Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Công nghệ sử dụng</h2>
                <p class="lead text-muted">Stack công nghệ hiện đại và đáng tin cậy</p>
            </div>
            
            <div class="text-center">
                <span class="tech-badge"><i class="fab fa-php me-2"></i>PHP 8+</span>
                <span class="tech-badge"><i class="fas fa-database me-2"></i>MySQL</span>
                <span class="tech-badge"><i class="fab fa-bootstrap me-2"></i>Bootstrap 5</span>
                <span class="tech-badge"><i class="fab fa-js me-2"></i>JavaScript ES6+</span>
                <span class="tech-badge"><i class="fas fa-key me-2"></i>JWT</span>
                <span class="tech-badge"><i class="fas fa-server me-2"></i>REST API</span>
                <span class="tech-badge"><i class="fab fa-font-awesome me-2"></i>Font Awesome</span>
                <span class="tech-badge"><i class="fab fa-google me-2"></i>Google Fonts</span>
            </div>
        </div>
    </section>

    <!-- Demo Accounts Section -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Tài khoản demo</h2>
                <p class="lead text-muted">Sử dụng các tài khoản sau để trải nghiệm hệ thống</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="stats-card">
                        <div class="mb-3">
                            <i class="fas fa-crown" style="font-size: 2rem; color: var(--warning-color);"></i>
                        </div>
                        <h5 class="fw-bold">Admin Account</h5>
                        <div class="stats-number">admin</div>
                        <div class="stats-label mb-3">Username: admin<br>Password: admin123</div>
                        <p class="text-muted small">Có quyền thêm, sửa, xóa sản phẩm và danh mục</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="stats-card">
                        <div class="mb-3">
                            <i class="fas fa-user" style="font-size: 2rem; color: var(--primary-color);"></i>
                        </div>
                        <h5 class="fw-bold">User Account</h5>
                        <div class="stats-number">user</div>
                        <div class="stats-label mb-3">Username: user<br>Password: admin123</div>
                        <p class="text-muted small">Chỉ có quyền xem danh sách sản phẩm và danh mục</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5" style="background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%);">
        <div class="container text-center text-white">
            <h2 class="display-5 fw-bold mb-3">Sẵn sàng trải nghiệm?</h2>
            <p class="lead mb-4">Đăng nhập ngay để khám phá tất cả tính năng của hệ thống</p>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="/webbanhang/account/login" class="demo-button">
                    <i class="fas fa-rocket me-2"></i>Bắt đầu ngay
                </a>
                <a href="/webbanhang/final_jwt_test.html" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-code me-2"></i>Test API
                </a>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add fade-in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card, .stats-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
