<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - WEBBANHANG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .test-card { margin-bottom: 20px; }
        .test-result { font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-pending { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-vial"></i> System Test - WEBBANHANG</h1>
        <p class="text-muted">Kiểm tra toàn bộ hệ thống API và Frontend</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> Backend API Tests</h5>
                    </div>
                    <div class="card-body">
                        <div id="api-tests">
                            <div class="test-item" data-test="database">
                                <i class="fas fa-circle status-pending"></i> Database Connection
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="get-products">
                                <i class="fas fa-circle status-pending"></i> GET /api/product
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="get-categories">
                                <i class="fas fa-circle status-pending"></i> GET /api/category
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="post-product">
                                <i class="fas fa-circle status-pending"></i> POST /api/product
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="put-product">
                                <i class="fas fa-circle status-pending"></i> PUT /api/product/{id}
                                <div class="test-result" style="display:none;"></div>
                            </div>
                        </div>
                        <button class="btn btn-primary mt-3" onclick="runApiTests()">
                            <i class="fas fa-play"></i> Run API Tests
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-desktop"></i> Frontend Tests</h5>
                    </div>
                    <div class="card-body">
                        <div id="frontend-tests">
                            <div class="test-item" data-test="jquery">
                                <i class="fas fa-circle status-pending"></i> jQuery Library
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="bootstrap">
                                <i class="fas fa-circle status-pending"></i> Bootstrap CSS/JS
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="fontawesome">
                                <i class="fas fa-circle status-pending"></i> Font Awesome Icons
                                <div class="test-result" style="display:none;"></div>
                            </div>
                            <div class="test-item" data-test="frontend-api">
                                <i class="fas fa-circle status-pending"></i> Frontend API Integration
                                <div class="test-result" style="display:none;"></div>
                            </div>
                        </div>
                        <button class="btn btn-success mt-3" onclick="runFrontendTests()">
                            <i class="fas fa-play"></i> Run Frontend Tests
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Test Summary</h5>
            </div>
            <div class="card-body">
                <div id="test-summary">
                    <p>Click "Run Tests" để bắt đầu kiểm tra hệ thống.</p>
                </div>
                <div class="mt-3">
                    <a href="frontend/demo.html" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Demo
                    </a>
                    <a href="frontend/index.html" class="btn btn-primary">
                        <i class="fas fa-rocket"></i> Launch App
                    </a>
                    <a href="API_DOCUMENTATION.md" class="btn btn-secondary" target="_blank">
                        <i class="fas fa-book"></i> API Docs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let testResults = {
            api: {},
            frontend: {},
            total: 0,
            passed: 0,
            failed: 0
        };

        function updateTestStatus(testName, status, message = '') {
            const testItem = $(`.test-item[data-test="${testName}"]`);
            const icon = testItem.find('i');
            const result = testItem.find('.test-result');
            
            icon.removeClass('status-pending status-pass status-fail');
            
            if (status === 'pass') {
                icon.addClass('status-pass');
                testResults.passed++;
            } else if (status === 'fail') {
                icon.addClass('status-fail');
                testResults.failed++;
            }
            
            testResults.total++;
            
            if (message) {
                result.text(message).show();
            }
            
            updateSummary();
        }

        function updateSummary() {
            const summary = $('#test-summary');
            const passRate = testResults.total > 0 ? (testResults.passed / testResults.total * 100).toFixed(1) : 0;
            
            summary.html(`
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">${testResults.total}</h4>
                            <small>Total Tests</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">${testResults.passed}</h4>
                            <small>Passed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger">${testResults.failed}</h4>
                            <small>Failed</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">${passRate}%</h4>
                            <small>Pass Rate</small>
                        </div>
                    </div>
                </div>
            `);
        }

        async function runApiTests() {
            // Reset counters
            testResults.api = {};
            
            // Test Database Connection
            try {
                const response = await $.ajax({
                    url: 'api/product',
                    method: 'GET',
                    timeout: 5000
                });
                updateTestStatus('database', 'pass', 'Connected successfully');
                updateTestStatus('get-products', 'pass', `${response.length} products loaded`);
            } catch (error) {
                updateTestStatus('database', 'fail', 'Connection failed: ' + error.statusText);
                updateTestStatus('get-products', 'fail', 'Failed to load products');
            }

            // Test Categories
            try {
                const response = await $.ajax({
                    url: 'api/category',
                    method: 'GET',
                    timeout: 5000
                });
                updateTestStatus('get-categories', 'pass', `${response.length} categories loaded`);
            } catch (error) {
                updateTestStatus('get-categories', 'fail', 'Failed to load categories');
            }

            // Test POST Product
            try {
                const testProduct = {
                    name: 'Test Product ' + Date.now(),
                    description: 'Test Description',
                    price: 99999,
                    category_id: 1
                };
                
                const response = await $.ajax({
                    url: 'api/product',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(testProduct),
                    timeout: 5000
                });
                
                updateTestStatus('post-product', 'pass', 'Product created successfully');
                
                // Test PUT Product (update the created product)
                const updateData = {
                    name: 'Updated Test Product',
                    description: 'Updated Description',
                    price: 199999,
                    category_id: 1
                };
                
                // Get the latest product to update
                const products = await $.ajax({url: 'api/product', method: 'GET'});
                const lastProduct = products[products.length - 1];
                
                await $.ajax({
                    url: `api/product/${lastProduct.id}`,
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify(updateData),
                    timeout: 5000
                });
                
                updateTestStatus('put-product', 'pass', 'Product updated successfully');
                
            } catch (error) {
                updateTestStatus('post-product', 'fail', 'Failed to create product');
                updateTestStatus('put-product', 'fail', 'Failed to update product');
            }
        }

        function runFrontendTests() {
            // Test jQuery
            if (typeof $ !== 'undefined') {
                updateTestStatus('jquery', 'pass', 'jQuery ' + $.fn.jquery + ' loaded');
            } else {
                updateTestStatus('jquery', 'fail', 'jQuery not found');
            }

            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                updateTestStatus('bootstrap', 'pass', 'Bootstrap loaded successfully');
            } else {
                updateTestStatus('bootstrap', 'fail', 'Bootstrap not found');
            }

            // Test Font Awesome
            const faTest = $('<i class="fas fa-test"></i>').appendTo('body');
            const faLoaded = window.getComputedStyle(faTest[0], ':before').fontFamily.includes('Font Awesome');
            faTest.remove();
            
            if (faLoaded) {
                updateTestStatus('fontawesome', 'pass', 'Font Awesome icons loaded');
            } else {
                updateTestStatus('fontawesome', 'fail', 'Font Awesome not loaded properly');
            }

            // Test Frontend API Integration
            $.ajax({
                url: 'api/product',
                method: 'GET',
                timeout: 3000
            })
            .done(function() {
                updateTestStatus('frontend-api', 'pass', 'Frontend can communicate with API');
            })
            .fail(function() {
                updateTestStatus('frontend-api', 'fail', 'Frontend cannot reach API');
            });
        }

        // Auto-run tests on page load
        $(document).ready(function() {
            setTimeout(() => {
                runFrontendTests();
                setTimeout(() => {
                    runApiTests();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
