<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Edit Product</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Debug Edit Product</h1>
        
        <!-- Quick Login -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Quick Login</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="quickLogin('admin', 'admin123')">Login as Admin</button>
                <div id="loginResult" class="mt-2"></div>
            </div>
        </div>

        <!-- Debug Tests -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Debug Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Individual API Calls</h6>
                        <button class="btn btn-info w-100 mb-2" onclick="testGetProduct()">Test GET Product/1</button>
                        <button class="btn btn-info w-100 mb-2" onclick="testGetCategories()">Test GET Categories</button>
                        <button class="btn btn-info w-100 mb-2" onclick="testGetCategoriesWithAuth()">Test GET Categories (with Auth)</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Combined Tests</h6>
                        <button class="btn btn-warning w-100 mb-2" onclick="testPromiseAll()">Test Promise.all (like edit page)</button>
                        <button class="btn btn-success w-100 mb-2" onclick="testDropdownPopulation()">Test Dropdown Population</button>
                    </div>
                </div>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Dropdown Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📋 Dropdown Test</h5>
            </div>
            <div class="card-body">
                <label for="test-category" class="form-label">Test Category Dropdown:</label>
                <select id="test-category" class="form-select">
                    <option value="">-- Chọn danh mục --</option>
                </select>
                <button class="btn btn-primary mt-2" onclick="populateTestDropdown()">Populate Dropdown</button>
                <div id="dropdownResult" class="mt-2"></div>
            </div>
        </div>

        <!-- Links -->
        <div class="card">
            <div class="card-header">
                <h5>🔗 Direct Links</h5>
            </div>
            <div class="card-body">
                <a href="/webbanhang/Product/edit/1" class="btn btn-outline-warning me-2" target="_blank">Edit Product 1 (Web)</a>
                <a href="/webbanhang/Product/add" class="btn btn-outline-success me-2" target="_blank">Add Product (Web)</a>
                <a href="/webbanhang/api/category" class="btn btn-outline-info me-2" target="_blank">API Categories (JSON)</a>
            </div>
        </div>
    </div>

    <script>
        // Quick login
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ Logged in as ${username}</div>`;
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test individual API calls
        async function testGetProduct() {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = {};
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/webbanhang/api/product/1', { headers });
                const data = await response.json();
                
                showResult('GET Product/1', response.status, data);
            } catch (error) {
                showResult('GET Product/1', 'ERROR', { error: error.message });
            }
        }

        async function testGetCategories() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const data = await response.json();
                
                showResult('GET Categories (no auth)', response.status, data);
            } catch (error) {
                showResult('GET Categories (no auth)', 'ERROR', { error: error.message });
            }
        }

        async function testGetCategoriesWithAuth() {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = {};
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/webbanhang/api/category', { headers });
                const data = await response.json();
                
                showResult('GET Categories (with auth)', response.status, data);
            } catch (error) {
                showResult('GET Categories (with auth)', 'ERROR', { error: error.message });
            }
        }

        // Test Promise.all like in edit page
        async function testPromiseAll() {
            const token = localStorage.getItem('jwtToken');
            try {
                const headers = {};
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const results = await Promise.all([
                    fetch('/webbanhang/api/product/1', { headers }).then(res => res.json()),
                    fetch('/webbanhang/api/category', { headers }).then(res => res.json())
                ]);

                showResult('Promise.all [product, categories]', 'SUCCESS', {
                    product: results[0],
                    categories: results[1]
                });
            } catch (error) {
                showResult('Promise.all', 'ERROR', { error: error.message });
            }
        }

        // Test dropdown population
        async function testDropdownPopulation() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const categories = await response.json();
                
                if (Array.isArray(categories)) {
                    showResult('Dropdown Test', 'SUCCESS', {
                        message: `Found ${categories.length} categories`,
                        categories: categories
                    });
                } else {
                    showResult('Dropdown Test', 'ERROR', {
                        message: 'Categories is not an array',
                        data: categories
                    });
                }
            } catch (error) {
                showResult('Dropdown Test', 'ERROR', { error: error.message });
            }
        }

        // Populate test dropdown
        async function populateTestDropdown() {
            const dropdown = document.getElementById('test-category');
            const resultDiv = document.getElementById('dropdownResult');
            
            try {
                // Clear existing options (except first one)
                while (dropdown.children.length > 1) {
                    dropdown.removeChild(dropdown.lastChild);
                }

                const response = await fetch('/webbanhang/api/category');
                const categories = await response.json();
                
                if (Array.isArray(categories)) {
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        dropdown.appendChild(option);
                    });
                    
                    resultDiv.innerHTML = 
                        `<div class="alert alert-success">✅ Populated ${categories.length} categories</div>`;
                } else {
                    resultDiv.innerHTML = 
                        `<div class="alert alert-danger">❌ Categories is not an array: ${JSON.stringify(categories)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Helper function to show results
        function showResult(operation, status, data) {
            const statusClass = (status === 'SUCCESS' || (typeof status === 'number' && status >= 200 && status < 300)) ? 'success' : 'danger';
            document.getElementById('testResult').innerHTML = 
                `<div class="alert alert-${statusClass}">
                    <strong>${operation}</strong><br>
                    Status: ${status}<br>
                    <details>
                        <summary>Response Data</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>`;
        }
    </script>
</body>
</html>
