<?php include 'app/views/shares/header.php'; ?>

<div class="container">
    <h1 class="my-4">S<PERSON>a danh mục</h1>
    
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <div class="card shadow-sm border-0" style="border-radius: 1rem;">
                <div class="card-header bg-warning text-dark" style="border-radius: 1rem 1rem 0 0;">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Chỉnh sửa thông tin danh mục
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div id="errorMessage" class="alert alert-danger d-none"></div>
                    <div id="successMessage" class="alert alert-success d-none"></div>
                    <div id="loading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <form id="categoryForm" novalidate>
                        <input type="hidden" id="id" name="id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-2 text-primary"></i>
                                        Tên danh mục <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="name" name="name"
                                           required placeholder="Nhập tên danh mục">
                                    <div class="invalid-feedback">
                                        Vui lòng nhập tên danh mục.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="slug" class="form-label fw-semibold">
                                        <i class="fas fa-link me-2 text-info"></i>
                                        Slug (URL thân thiện)
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="slug" name="slug"
                                           placeholder="tu-dong-tao-tu-ten-danh-muc">
                                    <div class="form-text">Để trống để tự động tạo từ tên danh mục</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold">
                                <i class="fas fa-align-left me-2 text-secondary"></i>
                                Mô tả danh mục <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="6"
                                      required placeholder="Nhập mô tả chi tiết về danh mục..."></textarea>
                            <div class="invalid-feedback">
                                Vui lòng nhập mô tả danh mục.
                            </div>
                            <div class="form-text">Mô tả giúp khách hàng hiểu rõ hơn về danh mục sản phẩm</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="sort_order" class="form-label fw-semibold">
                                        <i class="fas fa-sort-numeric-up me-2 text-success"></i>
                                        Thứ tự hiển thị
                                    </label>
                                    <input type="number" class="form-control form-control-lg" id="sort_order"
                                           name="sort_order" min="0" placeholder="0">
                                    <div class="form-text">Số nhỏ hơn sẽ hiển thị trước</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="is_active" class="form-label fw-semibold">
                                        <i class="fas fa-toggle-on me-2 text-warning"></i>
                                        Trạng thái
                                    </label>
                                    <select class="form-select form-select-lg" id="is_active" name="is_active">
                                        <option value="1">Kích hoạt</option>
                                        <option value="0">Tạm ẩn</option>
                                    </select>
                                    <div class="form-text">Danh mục bị ẩn sẽ không hiển thị trên website</div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Các trường có dấu <span class="text-danger">*</span> là bắt buộc
                                </small>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="/webbanhang/Category" class="btn btn-outline-secondary btn-lg px-4">
                                    <i class="fas fa-times me-2"></i>Hủy
                                </a>
                                <button type="submit" class="btn btn-warning btn-lg px-4">
                                    <i class="fas fa-save me-2"></i>Cập nhật danh mục
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById('categoryForm');
    const errorMessage = document.getElementById('errorMessage');
    const successMessage = document.getElementById('successMessage');
    const loading = document.getElementById('loading');

    function showLoading() {
        loading.classList.remove('d-none');
    }

    function hideLoading() {
        loading.classList.add('d-none');
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
        successMessage.classList.add('d-none');
    }

    function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('d-none');
        errorMessage.classList.add('d-none');
    }

    // Lấy ID từ URL
    const pathParts = window.location.pathname.split('/');
    const categoryId = pathParts[pathParts.length - 1];

    // Load category data
    showLoading();
    fetch(`/webbanhang/api/category/${categoryId}`, {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('jwtToken')}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Không thể tải thông tin danh mục');
        }
        return response.json();
    })
    .then(category => {
        // Fill category data
        document.getElementById('id').value = category.id;
        document.getElementById('name').value = category.name;
        document.getElementById('description').value = category.description;
    })
    .catch(error => {
        showError(error.message);
    })
    .finally(() => {
        hideLoading();
    });

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Reset messages
        errorMessage.classList.add('d-none');
        successMessage.classList.add('d-none');

        // Form validation
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = new FormData(this);
        const jsonData = Object.fromEntries(formData.entries());

        showLoading();

        const token = localStorage.getItem('jwtToken');
        fetch(`/webbanhang/api/category/${jsonData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || data.message || 'Lỗi khi cập nhật danh mục');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.message === 'Category updated successfully') {
                showSuccess('Cập nhật danh mục thành công!');
                setTimeout(() => {
                    location.href = '/webbanhang/Category';
                }, 1000);
            } else {
                throw new Error(data.error || 'Cập nhật danh mục thất bại');
            }
        })
        .catch(error => {
            showError(error.message);
        })
        .finally(() => {
            hideLoading();
        });
    });
});
</script>
