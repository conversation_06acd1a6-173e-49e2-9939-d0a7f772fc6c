<?php
class CategoryModel
{
    private $conn;
    private $table_name = "category";

    public function __construct($db)
    {
        $this->conn = $db;
    }

    // Lấy tất cả danh mục
    public function getCategories()
    {
        $query = "SELECT id, name, description FROM " . $this->table_name . " ORDER BY name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetchAll(PDO::FETCH_OBJ);
        return $result;
    }

    // Lấy danh mục theo ID
    public function getCategoryById($id)
    {
        $query = "SELECT id, name, description FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }

    // Thêm danh mục mới
    public function addCategory($name, $description)
    {
        $errors = [];

        // Validate input
        if (empty(trim($name))) {
            $errors['name'] = 'Tên danh mục không được để trống';
        }

        if (empty(trim($description))) {
            $errors['description'] = 'Mô tả không được để trống';
        }

        // Kiểm tra tên danh mục đã tồn tại chưa
        $checkQuery = "SELECT id FROM " . $this->table_name . " WHERE name = :name";
        $checkStmt = $this->conn->prepare($checkQuery);
        $checkStmt->bindParam(':name', $name);
        $checkStmt->execute();

        if ($checkStmt->fetch()) {
            $errors['name'] = 'Tên danh mục đã tồn tại';
        }

        if (!empty($errors)) {
            return $errors;
        }

        $query = "INSERT INTO " . $this->table_name . " (name, description) VALUES (:name, :description)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':description', $description);

        return $stmt->execute();
    }

    // Cập nhật danh mục
    public function updateCategory($id, $name, $description)
    {
        $errors = [];

        // Validate input
        if (empty(trim($name))) {
            $errors['name'] = 'Tên danh mục không được để trống';
        }

        if (empty(trim($description))) {
            $errors['description'] = 'Mô tả không được để trống';
        }

        // Kiểm tra tên danh mục đã tồn tại chưa (trừ chính nó)
        $checkQuery = "SELECT id FROM " . $this->table_name . " WHERE name = :name AND id != :id";
        $checkStmt = $this->conn->prepare($checkQuery);
        $checkStmt->bindParam(':name', $name);
        $checkStmt->bindParam(':id', $id);
        $checkStmt->execute();

        if ($checkStmt->fetch()) {
            $errors['name'] = 'Tên danh mục đã tồn tại';
        }

        if (!empty($errors)) {
            return $errors;
        }

        $query = "UPDATE " . $this->table_name . " SET name = :name, description = :description WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':description', $description);

        return $stmt->execute();
    }

    // Xóa danh mục
    public function deleteCategory($id)
    {
        // Kiểm tra xem danh mục có sản phẩm nào không
        $checkQuery = "SELECT COUNT(*) as count FROM product WHERE category_id = :id";
        $checkStmt = $this->conn->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id);
        $checkStmt->execute();
        $result = $checkStmt->fetch(PDO::FETCH_OBJ);

        if ($result->count > 0) {
            return ['error' => 'Không thể xóa danh mục này vì còn có sản phẩm thuộc danh mục'];
        }

        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }
}
?>