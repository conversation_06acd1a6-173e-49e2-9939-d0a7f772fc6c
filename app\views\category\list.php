<?php include 'app/views/shares/header.php'; ?>

<div class="container">
    <h1 class="my-4"><PERSON><PERSON> s<PERSON>ch danh mục</h1>
    <a href="/webbanhang/Category/add" class="btn btn-success mb-3" id="add-category-btn" style="display: none;">Thêm danh mục mới</a>
    <div id="loading" class="text-center d-none">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <ul class="list-group" id="category-list"></ul>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
const showLoading = () => document.getElementById('loading').classList.remove('d-none');
const hideLoading = () => document.getElementById('loading').classList.add('d-none');

// Function để decode JWT token
function decodeJWT(token) {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        return JSON.parse(jsonPayload);
    } catch (error) {
        return null;
    }
}

// Function kiểm tra user có phải admin không
function isAdmin() {
    const token = localStorage.getItem('jwtToken');
    if (!token) return false;
    
    const decoded = decodeJWT(token);
    return decoded && decoded.data && decoded.data.role === 'admin';
}

document.addEventListener("DOMContentLoaded", async function() {
    const token = localStorage.getItem('jwtToken');
    if (!token) {
        alert('Vui lòng đăng nhập');
        location.href = '/webbanhang/account/login';
        return;
    }

    // Hiển thị nút thêm danh mục chỉ cho admin
    if (isAdmin()) {
        document.getElementById('add-category-btn').style.display = 'inline-block';
    }

    try {
        showLoading();
        const response = await fetch('/webbanhang/api/category', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();
        const categoryList = document.getElementById('category-list');
        
        data.forEach(category => {
            const categoryItem = document.createElement('li');
            categoryItem.className = 'list-group-item';
            
            // Tạo nút admin chỉ khi user là admin
            const adminButtons = isAdmin() ? `
                <div class="btn-group" role="group">
                    <a href="/webbanhang/Category/edit/${category.id}" class="btn btn-warning btn-sm">Sửa</a>
                    <button class="btn btn-danger btn-sm ml-2" onclick="deleteCategory(${category.id})">Xóa</button>
                </div>
            ` : '';
            
            categoryItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">${category.name}</h5>
                        <p class="mb-1">${category.description}</p>
                    </div>
                    ${adminButtons}
                </div>
            `;
            categoryList.appendChild(categoryItem);
        });
    } catch (error) {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi tải danh sách danh mục');
    } finally {
        hideLoading();
    }
});

async function deleteCategory(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
        return;
    }

    // Kiểm tra quyền admin trước khi xóa
    if (!isAdmin()) {
        alert('Bạn không có quyền xóa danh mục!');
        return;
    }

    const token = localStorage.getItem('jwtToken');
    try {
        const response = await fetch(`/webbanhang/api/category/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();
        if (response.ok && data.message === 'Category deleted successfully') {
            location.reload();
        } else {
            throw new Error(data.error || data.message || 'Xóa danh mục thất bại');
        }
    } catch (error) {
        console.error('Error:', error);
        alert(error.message || 'Có lỗi xảy ra khi xóa danh mục');
    }
}
</script>
