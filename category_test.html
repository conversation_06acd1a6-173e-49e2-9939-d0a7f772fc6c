<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Management Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>📁 Category Management Test</h1>
        
        <!-- Login Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Quick Login</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="loginAs('admin', 'admin123')">Login as Admin</button>
                <button class="btn btn-secondary me-2" onclick="loginAs('user', 'admin123')">Login as User</button>
                <button class="btn btn-outline-danger" onclick="clearToken()">Logout</button>
                <div id="loginStatus" class="mt-2"></div>
            </div>
        </div>

        <!-- Category CRUD Tests -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📁 Category CRUD Operations</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Read Operations (Public)</h6>
                        <button class="btn btn-info w-100 mb-2" onclick="getAllCategories()">GET All Categories</button>
                        <button class="btn btn-info w-100 mb-2" onclick="getCategoryById()">GET Category by ID</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Write Operations (Admin Only)</h6>
                        <button class="btn btn-success w-100 mb-2" onclick="createCategory()">CREATE Category</button>
                        <button class="btn btn-warning w-100 mb-2" onclick="updateCategory()">UPDATE Category</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="deleteCategory()">DELETE Category</button>
                    </div>
                </div>
                <div id="categoryResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Web Interface Tests -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🌐 Web Interface Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="/webbanhang/Category" class="btn btn-outline-primary w-100 mb-2">Category List Page</a>
                    </div>
                    <div class="col-md-4">
                        <a href="/webbanhang/Category/add" class="btn btn-outline-success w-100 mb-2">Add Category Page</a>
                    </div>
                    <div class="col-md-4">
                        <a href="/webbanhang/Category/edit/1" class="btn btn-outline-warning w-100 mb-2">Edit Category Page</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="card">
            <div class="card-header">
                <h5>📊 Current Status</h5>
            </div>
            <div class="card-body">
                <div id="statusInfo"></div>
                <button class="btn btn-outline-primary" onclick="checkStatus()">Refresh Status</button>
            </div>
        </div>
    </div>

    <script>
        // Decode JWT token
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Login function
        async function loginAs(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    const decoded = decodeJWT(data.token);
                    document.getElementById('loginStatus').innerHTML = 
                        `<div class="alert alert-success">✅ Logged in as <strong>${username}</strong> (${decoded.data.role})</div>`;
                    checkStatus();
                } else {
                    document.getElementById('loginStatus').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginStatus').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Category API functions
        async function getAllCategories() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const data = await response.json();
                
                showResult('GET /api/category', response.status, data);
            } catch (error) {
                showResult('GET /api/category', 'ERROR', { error: error.message });
            }
        }

        async function getCategoryById() {
            try {
                const response = await fetch('/webbanhang/api/category/1');
                const data = await response.json();
                
                showResult('GET /api/category/1', response.status, data);
            } catch (error) {
                showResult('GET /api/category/1', 'ERROR', { error: error.message });
            }
        }

        async function createCategory() {
            const token = localStorage.getItem('jwtToken');
            const categoryData = {
                name: 'Test Category ' + Date.now(),
                description: 'Created via test at ' + new Date().toLocaleString()
            };

            try {
                const response = await fetch('/webbanhang/api/category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(categoryData)
                });

                const data = await response.json();
                showResult('POST /api/category', response.status, data);
            } catch (error) {
                showResult('POST /api/category', 'ERROR', { error: error.message });
            }
        }

        async function updateCategory() {
            const token = localStorage.getItem('jwtToken');
            const categoryData = {
                name: 'Updated Test Category',
                description: 'Updated via test at ' + new Date().toLocaleString()
            };

            try {
                const response = await fetch('/webbanhang/api/category/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(categoryData)
                });

                const data = await response.json();
                showResult('PUT /api/category/1', response.status, data);
            } catch (error) {
                showResult('PUT /api/category/1', 'ERROR', { error: error.message });
            }
        }

        async function deleteCategory() {
            const token = localStorage.getItem('jwtToken');

            try {
                const response = await fetch('/webbanhang/api/category/999', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                showResult('DELETE /api/category/999', response.status, data);
            } catch (error) {
                showResult('DELETE /api/category/999', 'ERROR', { error: error.message });
            }
        }

        // Helper functions
        function showResult(operation, status, data) {
            const statusClass = (status >= 200 && status < 300) ? 'success' : 'danger';
            document.getElementById('categoryResult').innerHTML = 
                `<div class="alert alert-${statusClass}">
                    <strong>${operation}</strong><br>
                    Status: ${status}<br>
                    Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>`;
        }

        function clearToken() {
            localStorage.removeItem('jwtToken');
            document.getElementById('loginStatus').innerHTML = 
                '<div class="alert alert-info">Logged out</div>';
            checkStatus();
        }

        function checkStatus() {
            const token = localStorage.getItem('jwtToken');
            let statusHTML = '<h6>Authentication Status:</h6>';
            
            if (token) {
                const decoded = decodeJWT(token);
                if (decoded) {
                    statusHTML += `
                        <div class="alert alert-info">
                            <strong>Logged in as:</strong> ${decoded.data.username}<br>
                            <strong>Role:</strong> ${decoded.data.role}<br>
                            <strong>Expires:</strong> ${new Date(decoded.exp * 1000).toLocaleString()}
                        </div>`;
                } else {
                    statusHTML += '<div class="alert alert-warning">Invalid token</div>';
                }
            } else {
                statusHTML += '<div class="alert alert-secondary">Not logged in</div>';
            }
            
            document.getElementById('statusInfo').innerHTML = statusHTML;
        }

        // Check status on page load
        checkStatus();
    </script>
</body>
</html>
