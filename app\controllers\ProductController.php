<?php

require_once 'app/config/database.php';
require_once 'app/models/ProductModel.php';
require_once 'app/models/CategoryModel.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/middleware/JWTMiddleware.php';

class ProductController {
    private $productModel;
    private $db;
    private $jwtMiddleware;

    public function __construct() {
        $this->db = (new Database())->getConnection();
        $this->productModel = new ProductModel($this->db);
        $this->jwtMiddleware = new JWTMiddleware();
    }

    // Kiểm tra quyền Admin (hỗ trợ cả Session và JWT)
    private function isAdmin() {
        // Kiểm tra Session trước (cho web interface)
        try {
            if (SessionHelper::isAdmin()) {
                return true;
            }
        } catch (Exception $e) {
            // Ignore session errors
        }

        // Nếu không có Session, kiểm tra JWT (cho API)
        try {
            $userData = $this->jwtMiddleware->authenticate();
            return $userData && $this->jwtMiddleware->isAdmin($userData);
        } catch (Exception $e) {
            return false;
        }
    }

    // Kiểm tra user (hỗ trợ cả Session và JWT)
    private function isUser() {
        // Kiểm tra Session trước (cho web interface)
        try {
            if (SessionHelper::hasRole('user')) {
                return true;
            }
        } catch (Exception $e) {
            // Ignore session errors
        }

        // Nếu không có Session, kiểm tra JWT (cho API)
        try {
            $userData = $this->jwtMiddleware->authenticate();
            return $userData && $this->jwtMiddleware->hasRole($userData, 'user');
        } catch (Exception $e) {
            return false;
        }
    }

    // Hiển thị danh sách sản phẩm (mở cho tất cả) 
    public function index() { 
        $products = $this->productModel->getProducts(); 
        include 'app/views/product/list.php'; 
    } 

    // Xem chi tiết sản phẩm (mở cho tất cả) 
    public function show($id) { 
        $product = $this->productModel->getProductById($id); 
        if ($product) { 
            include 'app/views/product/show.php'; 
        } else { 
            echo "Không thấy sản phẩm."; 
        } 
    } 

    // Thêm sản phẩm (chỉ Admin) 
    public function add() { 
        if (!$this->isAdmin()) { 
            echo "Bạn không có quyền truy cập chức năng này!"; 
            exit; 
        }
        $categories = (new CategoryModel($this->db))->getCategories(); 
        include_once 'app/views/product/add.php'; 
    }

    // Lưu sản phẩm mới (chỉ Admin) 
    public function save() { 
        if (!$this->isAdmin()) { 
            echo "Bạn không có quyền truy cập chức năng này!"; 
            exit; 
        } 

        if ($_SERVER['REQUEST_METHOD'] == 'POST') { 
            $name = $_POST['name'] ?? ''; 
            $description = $_POST['description'] ?? ''; 
            $price = $_POST['price'] ?? ''; 
            $category_id = $_POST['category_id'] ?? null; 
            $image = (isset($_FILES['image']) && $_FILES['image']['error'] == 0)  
                ? $this->uploadImage($_FILES['image'])  
                : ""; 

            $result = $this->productModel->addProduct($name, $description, $price, $category_id, $image); 
            if (is_array($result)) { 
                $errors = $result; 
                $categories = (new CategoryModel($this->db))->getCategories(); 
                include 'app/views/product/add.php'; 
            } else { 
                header('Location: /webbanhang/Product'); 
            } 
        } 
    } 

    // Cập nhật sản phẩm (chỉ Admin) 
    public function update() { 
        if (!$this->isAdmin()) { 
            echo "Bạn không có quyền truy cập chức năng này!"; 
            exit; 
        } 

        if ($_SERVER['REQUEST_METHOD'] === 'POST') { 
            $id = $_POST['id']; 
            $name = $_POST['name']; 
            $description = $_POST['description']; 
            $price = $_POST['price']; 
            $category_id = $_POST['category_id']; 
            $image = (isset($_FILES['image']) && $_FILES['image']['error'] == 0)  
                ? $this->uploadImage($_FILES['image'])  
                : $_POST['existing_image']; 

            $edit = $this->productModel->updateProduct($id, $name, $description, $price, $category_id, $image); 
            if ($edit) { 
                header('Location: /webbanhang/Product'); 
            } else { 
                echo "Đã xảy ra lỗi khi lưu sản phẩm."; 
            } 
        } 
    } 

    // Thêm phương thức edit để hiển thị form sửa sản phẩm
    public function edit($id) {
        // Kiểm tra quyền admin
        if (!$this->isAdmin()) {
            echo "Bạn không có quyền truy cập chức năng này!";
            exit;
        }

        // Lấy thông tin sản phẩm cần sửa
        $product = $this->productModel->getProductById($id);

        if (!$product) {
            echo "Không tìm thấy sản phẩm!";
            exit;
        }

        // Lấy danh sách danh mục để hiển thị trong form
        $categories = (new CategoryModel($this->db))->getCategories();

        // Truyền ID để sử dụng trong JavaScript
        $editId = $id;

        // Hiển thị form edit
        include 'app/views/product/edit.php';
    }

    // Xóa sản phẩm (chỉ Admin) 
    public function delete($id) { 
        if (!$this->isAdmin()) { 
            echo "Bạn không có quyền truy cập chức năng này!"; 
            exit; 
        } 
        
        if ($this->productModel->deleteProduct($id)) { 
            header('Location: /webbanhang/Product'); 
        } else { 
            echo "Đã xảy ra lỗi khi xóa sản phẩm."; 
        } 
    } 

    /**
     * Xử lý upload hình ảnh sản phẩm
     * @param array $file Thông tin file từ $_FILES
     * @return string Tên file sau khi upload hoặc chuỗi rỗng nếu thất bại
     */
    private function uploadImage($file) {
        $target_dir = "public/uploads/";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        
        // Tạo tên file ngẫu nhiên để tránh trùng lặp
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $newFileName = uniqid() . '.' . $extension;
        $target_file = $target_dir . $newFileName;

        // Kiểm tra và cho phép một số định dạng ảnh phổ biến
        $allowTypes = array('jpg', 'jpeg', 'png', 'gif');
        if (!in_array(strtolower($extension), $allowTypes)) {
            return "";
        }

        if (move_uploaded_file($file["tmp_name"], $target_file)) {
            return $newFileName;
        }
        
        return "";
    }

    /**
     * Thêm sản phẩm vào giỏ hàng
     */
    public function addToCart($id) {
        // Kiểm tra phải là user mới được thêm vào giỏ
        if (!$this->isUser()) {
            echo "Chỉ tài khoản người dùng mới có thể mua hàng!";
            exit;
        }

        // Chỉ cần kiểm tra đăng nhập
        if (!SessionHelper::isLoggedIn()) {
            header('Location: /webbanhang/account/login');
            exit;
        }

        $product = $this->productModel->getProductById($id);
        if ($product) {
            SessionHelper::start();
            if (!isset($_SESSION['cart'])) {
                $_SESSION['cart'] = array();
            }

            // Nếu sản phẩm đã có trong giỏ hàng thì tăng số lượng
            if (isset($_SESSION['cart'][$id])) {
                $_SESSION['cart'][$id]['quantity']++;
            } else {
                // Thêm sản phẩm mới vào giỏ hàng
                $_SESSION['cart'][$id] = array(
                    'id' => $id,
                    'name' => $product->name,
                    'price' => $product->price,
                    'image' => $product->image,
                    'quantity' => 1
                );
            }
            header('Location: /webbanhang/Product/cart');
        } else {
            echo "Sản phẩm không tồn tại!";
        }
        exit;
    }

    /**
     * Hiển thị giỏ hàng
     */
    public function cart() {
        // Kiểm tra phải là user mới xem được giỏ hàng
        if (!$this->isUser()) {
            echo "Chỉ tài khoản người dùng mới có thể mua hàng!";
            exit;
        }
        // Chỉ cần kiểm tra đăng nhập 
        if (!SessionHelper::isLoggedIn()) {
            header('Location: /webbanhang/account/login');
            exit;
        }

        SessionHelper::start();
        $cart = $_SESSION['cart'] ?? array();
        include 'app/views/product/cart.php';
    }

    /**
     * Hiển thị form thanh toán
     */
    public function checkout() {
        // Kiểm tra phải là user mới thanh toán được
        if (!$this->isUser()) {
            echo "Chỉ tài khoản người dùng mới có thể mua hàng!";
            exit;
        }
        // Chỉ cần kiểm tra đăng nhập
        if (!SessionHelper::isLoggedIn()) {
            header('Location: /webbanhang/account/login');
            exit;
        }

        // Kiểm tra giỏ hàng có trống không
        SessionHelper::start();
        if (empty($_SESSION['cart'])) {
            header('Location: /webbanhang/Product/cart');
            exit;
        }

        include 'app/views/product/checkout.php';
    }

    /**
     * Xử lý thanh toán đơn hàng
     */
    public function processCheckout() {
        // Kiểm tra phải là user mới xử lý thanh toán được
        if (!$this->isUser()) {
            echo "Chỉ tài khoản người dùng mới có thể mua hàng!";
            exit;
        }
        // Chỉ cần kiểm tra đăng nhập
        if (!SessionHelper::isLoggedIn()) {
            header('Location: /webbanhang/account/login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            SessionHelper::start();
            
            if (empty($_SESSION['cart'])) {
                header('Location: /webbanhang/Product/cart');
                exit;
            }

            // Lấy thông tin từ form
            $name = $_POST['name'] ?? '';
            $phone = $_POST['phone'] ?? '';
            $address = $_POST['address'] ?? '';

            if (empty($name) || empty($phone) || empty($address)) {
                echo "Vui lòng điền đầy đủ thông tin!";
                exit;
            }

            // Tính tổng tiền
            $total = 0;
            foreach ($_SESSION['cart'] as $item) {
                $total += $item['price'] * $item['quantity'];
            }

            try {
                // Bắt đầu transaction
                $this->db->beginTransaction();

                // Tạo đơn hàng mới
                $query = "INSERT INTO orders (name, phone, address, total_amount, status) 
                         VALUES (:name, :phone, :address, :total, 'pending')";
                $stmt = $this->db->prepare($query);
                $stmt->execute([
                    ':name' => $name,
                    ':phone' => $phone,
                    ':address' => $address,
                    ':total' => $total
                ]);

                $orderId = $this->db->lastInsertId();

                // Thêm chi tiết đơn hàng
                $query = "INSERT INTO order_details (order_id, product_id, quantity, price) 
                         VALUES (:order_id, :product_id, :quantity, :price)";
                $stmt = $this->db->prepare($query);

                foreach ($_SESSION['cart'] as $item) {
                    $stmt->execute([
                        ':order_id' => $orderId,
                        ':product_id' => $item['id'],
                        ':quantity' => $item['quantity'],
                        ':price' => $item['price']
                    ]);
                }

                // Commit transaction
                $this->db->commit();

                // Xóa giỏ hàng
                unset($_SESSION['cart']);

                // Chuyển đến trang xác nhận
                header('Location: /webbanhang/Product/orderConfirmation');
                exit;

            } catch (Exception $e) {
                // Rollback nếu có lỗi
                $this->db->rollBack();
                echo "Đã xảy ra lỗi khi xử lý đơn hàng: " . $e->getMessage();
            }
        }
    }

    /**
     * Hiển thị trang xác nhận đơn hàng
     */
    public function orderConfirmation() {
        // Kiểm tra phải là user mới xem được trang xác nhận
        if (!$this->isUser()) {
            echo "Chỉ tài khoản người dùng mới có thể mua hàng!";
            exit;
        }
        // Chỉ cần kiểm tra đăng nhập
        if (!SessionHelper::isLoggedIn()) {
            header('Location: /webbanhang/account/login');
            exit;
        }
        include 'app/views/product/orderConfirmation.php';
    }
}
?>