<?php

require_once('app/config/database.php');
require_once('app/models/ProductModel.php');
require_once('app/models/CategoryModel.php');

class ProductApiController
{
    private $productModel;
    private $db;
    private $jwtHandler;

    public function __construct()
    {
        // Bắt đầu output buffering để tránh output không mong muốn
        if (!ob_get_level()) {
            ob_start();
        }

        $this->db = (new Database())->getConnection();
        $this->productModel = new ProductModel($this->db);
        $this->jwtHandler = new JWTHandler();
    }

    private function cleanOutput()
    {
        // <PERSON><PERSON><PERSON> bấ<PERSON> kỳ output buffer nào trước khi trả về JSON
        while (ob_get_level()) {
            ob_end_clean();
        }
    }

    // Kiểm tra JWT token và trả về user data
    private function authenticateJWT()
    {
        // Thử nhiều cách lấy Authorization header
        $authHeader = '';

        // Cách 1: getallheaders()
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        }

        // Cách 2: $_SERVER
        if (empty($authHeader)) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        }

        // Cách 3: apache_request_headers()
        if (empty($authHeader) && function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        }

        if (empty($authHeader)) {
            return null;
        }

        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            return $this->jwtHandler->decode($token);
        }

        return null;
    }

    // Kiểm tra quyền admin
    private function isAdmin($userData)
    {
        return $userData && isset($userData['role']) && $userData['role'] === 'admin';
    }

    // Trả về lỗi unauthorized
    private function unauthorizedResponse($message = 'Unauthorized')
    {
        http_response_code(401);
        echo json_encode(['error' => $message], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // Trả về lỗi forbidden
    private function forbiddenResponse($message = 'Forbidden')
    {
        http_response_code(403);
        echo json_encode(['error' => $message], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // Lấy danh sách sản phẩm (PUBLIC - không cần JWT)
    public function index()
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $products = $this->productModel->getProducts();

            // Đảm bảo encoding UTF-8
            $json = json_encode($products, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

            if ($json === false) {
                throw new Exception('JSON encoding failed: ' . json_last_error_msg());
            }

            echo $json;
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Lấy thông tin sản phẩm theo ID (PUBLIC - không cần JWT)
    public function show($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $product = $this->productModel->getProductById($id);

            if ($product) {
                echo json_encode($product, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Product not found'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Thêm sản phẩm mới (CẦN JWT + ADMIN)
    public function store()
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Kiểm tra JWT token
        $userData = $this->authenticateJWT();
        if (!$userData) {
            $this->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->isAdmin($userData)) {
            $this->forbiddenResponse('Chỉ admin mới có quyền thêm sản phẩm');
        }

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';
            $price = $data['price'] ?? '';
            $category_id = $data['category_id'] ?? null;

            $result = $this->productModel->addProduct(
                $name,
                $description,
                $price,
                $category_id
            );

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(201);
                echo json_encode(['message' => 'Product created successfully'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Cập nhật sản phẩm theo ID (CẦN JWT + ADMIN)
    public function update($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Kiểm tra JWT token
        $userData = $this->authenticateJWT();
        if (!$userData) {
            $this->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->isAdmin($userData)) {
            $this->forbiddenResponse('Chỉ admin mới có quyền cập nhật sản phẩm');
        }

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';
            $price = $data['price'] ?? '';
            $category_id = $data['category_id'] ?? null;

            $result = $this->productModel->updateProduct(
                $id,
                $name,
                $description,
                $price,
                $category_id
            );

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } elseif ($result) {
                echo json_encode(['message' => 'Product updated successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Product update failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Xóa sản phẩm theo ID (CẦN JWT + ADMIN)
    public function destroy($id)
    {
        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        // Kiểm tra JWT token
        $userData = $this->authenticateJWT();
        if (!$userData) {
            $this->unauthorizedResponse('Token không hợp lệ hoặc đã hết hạn');
        }

        // Kiểm tra quyền admin
        if (!$this->isAdmin($userData)) {
            $this->forbiddenResponse('Chỉ admin mới có quyền xóa sản phẩm');
        }

        try {
            $result = $this->productModel->deleteProduct($id);

            if ($result) {
                echo json_encode(['message' => 'Product deleted successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Product deletion failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }
}