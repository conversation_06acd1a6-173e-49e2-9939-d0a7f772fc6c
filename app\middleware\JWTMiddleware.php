<?php

require_once 'app/utils/JWTHandler.php';

class JWTMiddleware
{
    private $jwtHandler;

    public function __construct()
    {
        $this->jwtHandler = new JWTHandler();
    }

    /**
     * <PERSON><PERSON><PERSON> thực JWT token từ header Authorization
     * @return array|null Trả về user data nếu token hợp lệ, null nếu không
     */
    public function authenticate()
    {
        // Lấy Authorization header (hỗ trợ nhiều cách)
        $authHeader = null;
        
        // Cách 1: getallheaders() (nếu có)
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        }
        
        // Cách 2: $_SERVER (fallback)
        if (!$authHeader) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
        }

        if (!$authHeader) {
            return null;
        }

        // Kiểm tra format Bearer token
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }

        $token = $matches[1];

        // <PERSON><PERSON><PERSON><PERSON> mã token
        $userData = $this->jwtHandler->decode($token);

        return $userData;
    }

    /**
     * Kiểm tra user có role admin không
     * @param array $userData
     * @return bool
     */
    public function isAdmin($userData)
    {
        return isset($userData['role']) && $userData['role'] === 'admin';
    }

    /**
     * Kiểm tra user có role cụ thể không
     * @param array $userData
     * @param string $role
     * @return bool
     */
    public function hasRole($userData, $role)
    {
        return isset($userData['role']) && $userData['role'] === $role;
    }

    /**
     * Trả về response JSON lỗi xác thực
     */
    public function unauthorizedResponse($message = 'Unauthorized')
    {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => $message], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Trả về response JSON lỗi không có quyền
     */
    public function forbiddenResponse($message = 'Forbidden - Admin access required')
    {
        http_response_code(403);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => $message], JSON_UNESCAPED_UNICODE);
        exit;
    }
}
