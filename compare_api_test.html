<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compare Product vs Category API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Compare Product vs Category API</h1>
        <p class="lead">So sánh JWT authentication giữa Product và Category API</p>
        
        <!-- Login -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Login</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="login()">Login as Admin</button>
                <div id="loginResult" class="mt-2"></div>
            </div>
        </div>

        <!-- Comparison Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>⚖️ Side by Side Comparison</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">🛍️ Product API</h6>
                        <button class="btn btn-success w-100 mb-2" onclick="testProductGET()">GET Products</button>
                        <button class="btn btn-warning w-100 mb-2" onclick="testProductPOST()">POST Product</button>
                        <button class="btn btn-info w-100 mb-2" onclick="testProductPUT()">PUT Product</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="testProductDELETE()">DELETE Product</button>
                        <div id="productResult" class="mt-2"></div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">📂 Category API</h6>
                        <button class="btn btn-success w-100 mb-2" onclick="testCategoryGET()">GET Categories</button>
                        <button class="btn btn-warning w-100 mb-2" onclick="testCategoryPOST()">POST Category</button>
                        <button class="btn btn-info w-100 mb-2" onclick="testCategoryPUT()">PUT Category</button>
                        <button class="btn btn-danger w-100 mb-2" onclick="testCategoryDELETE()">DELETE Category</button>
                        <div id="categoryResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Token Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🎫 Token Info</h5>
            </div>
            <div class="card-body">
                <div id="tokenInfo"></div>
                <button class="btn btn-outline-primary" onclick="showTokenInfo()">Show Token Details</button>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card">
            <div class="card-header">
                <h5>🔧 Debug Info</h5>
            </div>
            <div class="card-body">
                <div id="debugInfo"></div>
            </div>
        </div>
    </div>

    <script>
        let currentToken = '';

        async function login() {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (data.token) {
                    currentToken = data.token;
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">✅ Login successful! Token ready.</div>`;
                    showTokenInfo();
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Product API Tests
        async function testProductGET() {
            try {
                const response = await fetch('/webbanhang/api/product');
                const data = await response.json();
                showResult('productResult', 'GET Products', response.status, data);
            } catch (error) {
                showResult('productResult', 'GET Products', 'ERROR', { error: error.message });
            }
        }

        async function testProductPOST() {
            if (!currentToken) {
                showResult('productResult', 'POST Product', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        name: 'Test Product ' + Date.now(),
                        description: 'Created via comparison test',
                        price: 100,
                        category_id: 1
                    })
                });

                const data = await response.json();
                showResult('productResult', 'POST Product', response.status, data);
            } catch (error) {
                showResult('productResult', 'POST Product', 'ERROR', { error: error.message });
            }
        }

        async function testProductPUT() {
            if (!currentToken) {
                showResult('productResult', 'PUT Product', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/product/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        name: 'Updated Product ' + Date.now(),
                        description: 'Updated via comparison test',
                        price: 200,
                        category_id: 1
                    })
                });

                const data = await response.json();
                showResult('productResult', 'PUT Product', response.status, data);
            } catch (error) {
                showResult('productResult', 'PUT Product', 'ERROR', { error: error.message });
            }
        }

        async function testProductDELETE() {
            if (!currentToken) {
                showResult('productResult', 'DELETE Product', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/product/999', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const data = await response.json();
                showResult('productResult', 'DELETE Product', response.status, data);
            } catch (error) {
                showResult('productResult', 'DELETE Product', 'ERROR', { error: error.message });
            }
        }

        // Category API Tests
        async function testCategoryGET() {
            try {
                const response = await fetch('/webbanhang/api/category');
                const data = await response.json();
                showResult('categoryResult', 'GET Categories', response.status, data);
            } catch (error) {
                showResult('categoryResult', 'GET Categories', 'ERROR', { error: error.message });
            }
        }

        async function testCategoryPOST() {
            if (!currentToken) {
                showResult('categoryResult', 'POST Category', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        name: 'Test Category ' + Date.now(),
                        description: 'Created via comparison test'
                    })
                });

                const responseText = await response.text();
                console.log('Category POST raw response:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = { rawResponse: responseText, parseError: e.message };
                }

                showResult('categoryResult', 'POST Category', response.status, data);
            } catch (error) {
                showResult('categoryResult', 'POST Category', 'ERROR', { error: error.message });
            }
        }

        async function testCategoryPUT() {
            if (!currentToken) {
                showResult('categoryResult', 'PUT Category', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/category/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        name: 'Updated Category ' + Date.now(),
                        description: 'Updated via comparison test'
                    })
                });

                const data = await response.json();
                showResult('categoryResult', 'PUT Category', response.status, data);
            } catch (error) {
                showResult('categoryResult', 'PUT Category', 'ERROR', { error: error.message });
            }
        }

        async function testCategoryDELETE() {
            if (!currentToken) {
                showResult('categoryResult', 'DELETE Category', 'ERROR', { error: 'No token. Please login first.' });
                return;
            }

            try {
                const response = await fetch('/webbanhang/api/category/999', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const data = await response.json();
                showResult('categoryResult', 'DELETE Category', response.status, data);
            } catch (error) {
                showResult('categoryResult', 'DELETE Category', 'ERROR', { error: error.message });
            }
        }

        function showResult(containerId, operation, status, data) {
            const statusClass = (status >= 200 && status < 300) ? 'success' : 'danger';
            const container = document.getElementById(containerId);
            
            container.innerHTML = 
                `<div class="alert alert-${statusClass} alert-sm">
                    <strong>${operation}</strong><br>
                    <small>Status: ${status}</small><br>
                    <details>
                        <summary>Response</summary>
                        <pre style="font-size: 0.8em; max-height: 150px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>`;
        }

        function showTokenInfo() {
            if (!currentToken) {
                document.getElementById('tokenInfo').innerHTML = 
                    '<div class="alert alert-warning">No token available</div>';
                return;
            }

            try {
                const parts = currentToken.split('.');
                const payload = JSON.parse(atob(parts[1]));
                
                document.getElementById('tokenInfo').innerHTML = 
                    `<div class="alert alert-info">
                        <h6>Token Details:</h6>
                        <strong>User:</strong> ${payload.data?.username}<br>
                        <strong>Role:</strong> ${payload.data?.role}<br>
                        <strong>Issued:</strong> ${new Date(payload.iat * 1000).toLocaleString()}<br>
                        <strong>Expires:</strong> ${new Date(payload.exp * 1000).toLocaleString()}<br>
                        <strong>Is Valid:</strong> ${payload.exp > Math.floor(Date.now() / 1000) ? '✅ Yes' : '❌ Expired'}
                    </div>`;
            } catch (error) {
                document.getElementById('tokenInfo').innerHTML = 
                    `<div class="alert alert-danger">Error parsing token: ${error.message}</div>`;
            }
        }

        // Auto-update debug info
        setInterval(() => {
            const debugInfo = {
                'Current Token': currentToken ? currentToken.substring(0, 30) + '...' : 'None',
                'Token Length': currentToken ? currentToken.length : 0,
                'Browser': navigator.userAgent.split(' ')[0],
                'Timestamp': new Date().toLocaleTimeString()
            };

            document.getElementById('debugInfo').innerHTML = 
                `<pre style="font-size: 0.9em;">${JSON.stringify(debugInfo, null, 2)}</pre>`;
        }, 5000);
    </script>
</body>
</html>
