<?php include 'app/views/shares/header.php'; ?>

<!-- Hero Section with modern design -->
<section class="hero-section">
    <div class="hero-background"></div>
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Chào mừng đến với
                        <span class="text-gradient">Web Bán Hàng</span>
                    </h1>
                    <p class="hero-subtitle">
                        Hệ thống quản lý bán hàng hiện đại với công nghệ JWT Authentication,
                        RESTful API và giao diện responsive đẹp mắt.
                    </p>
                    <div class="hero-buttons">
                        <a href="/webbanhang/Product" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-box me-2"></i>Quản lý sản phẩm
                        </a>
                        <a href="/webbanhang/Category" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-tags me-2"></i>Quản lý danh mục
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="floating-card">
                        <i class="fas fa-store"></i>
                        <h5>Web Bán Hàng</h5>
                        <p>Modern Management System</p>
                    </div>
                    <div class="floating-elements">
                        <div class="floating-element" style="top: 10%; left: 10%;"></div>
                        <div class="floating-element" style="top: 60%; right: 20%;"></div>
                        <div class="floating-element" style="bottom: 20%; left: 30%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        position: relative;
        overflow: hidden;
        padding: 5rem 0;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }

    .text-gradient {
        background: linear-gradient(45deg, #fbbf24, #f59e0b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        opacity: 0.9;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-buttons {
        margin-bottom: 2rem;
    }

    .hero-image {
        position: relative;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .floating-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2rem;
        text-align: center;
        animation: float 3s ease-in-out infinite;
    }

    .floating-card i {
        font-size: 3rem;
        color: var(--warning-color);
        margin-bottom: 1rem;
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .floating-element {
        position: absolute;
        width: 20px;
        height: 20px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        animation: float-up 4s ease-in-out infinite;
    }

    .floating-element:nth-child(2) {
        animation-delay: 1s;
        background: rgba(251, 191, 36, 0.3);
    }

    .floating-element:nth-child(3) {
        animation-delay: 2s;
        width: 15px;
        height: 15px;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    @keyframes float-up {
        0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }

    .min-vh-75 {
        min-height: 75vh;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-buttons .btn {
            display: block;
            width: 100%;
            margin-bottom: 1rem;
        }

        .hero-image {
            height: 300px;
            margin-top: 2rem;
        }
    }
</style>

<!-- Features Section -->
<div id="myCarousel" class="carousel slide mb-6" data-bs-ride="carousel">
    <div class="carousel-indicators">
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
        <button type="button" data-bs-target="#myCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
    </div>
    <div class="carousel-inner">
        <div class="carousel-item active">
            <div class="overlay-image" style="background-image: url('/webbanhang/uploads/carousel/carousel1.jpg');"></div>
            <div class="container">
                <div class="carousel-caption text-start">
                    <h1>Sản phẩm công nghệ hàng đầu</h1>
                    <p class="opacity-75">Khám phá các sản phẩm điện tử mới nhất với chất lượng tốt nhất.</p>
                    <p><a class="btn btn-lg btn-primary" href="/webbanhang/Product/?category=1">Xem ngay</a></p>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <div class="overlay-image" style="background-image: url('/webbanhang/uploads/carousel/carousel2.jpg');"></div>
            <div class="container">
                <div class="carousel-caption">
                    <h1>Laptop chính hãng</h1>
                    <p>Đa dạng mẫu mã, cấu hình mạnh mẽ, đáp ứng mọi nhu cầu.</p>
                    <p><a class="btn btn-lg btn-primary" href="/webbanhang/Product/?category=2">Tìm hiểu thêm</a></p>
                </div>
            </div>
        </div>
        <div class="carousel-item">
            <div class="overlay-image" style="background-image: url('/webbanhang/uploads/carousel/carousel3.jpg');"></div>
            <div class="container">
                <div class="carousel-caption text-end">
                    <h1>Phụ kiện chất lượng cao</h1>
                    <p>Đa dạng phụ kiện cho các thiết bị của bạn với giá cả hợp lý.</p>
                    <p><a class="btn btn-lg btn-primary" href="/webbanhang/Product/?category=4">Mua ngay</a></p>
                </div>
            </div>
        </div>
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#myCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#myCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
    </button>
</div>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-3">Tính năng nổi bật</h2>
            <p class="lead text-muted">Hệ thống được xây dựng với công nghệ hiện đại và best practices</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4">
                <div class="feature-card h-100">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">JWT Authentication</h4>
                    <p class="feature-description">
                        Bảo mật cao với JSON Web Token, phân quyền admin/user rõ ràng,
                        đảm bảo an toàn dữ liệu tuyệt đối.
                    </p>
                    <div class="feature-tech">
                        <span class="tech-badge">JWT</span>
                        <span class="tech-badge">Security</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="feature-card h-100">
                    <div class="feature-icon">
                        <i class="fas fa-api"></i>
                    </div>
                    <h4 class="feature-title">RESTful API</h4>
                    <p class="feature-description">
                        API chuẩn REST với CRUD operations đầy đủ,
                        hỗ trợ tích hợp dễ dàng với các hệ thống khác.
                    </p>
                    <div class="feature-tech">
                        <span class="tech-badge">REST</span>
                        <span class="tech-badge">API</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="feature-card h-100">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="feature-title">Responsive Design</h4>
                    <p class="feature-description">
                        Giao diện đẹp, hiện đại và tương thích mọi thiết bị,
                        từ desktop đến mobile với trải nghiệm tối ưu.
                    </p>
                    <div class="feature-tech">
                        <span class="tech-badge">Bootstrap 5</span>
                        <span class="tech-badge">Mobile-First</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .features-section {
        background: var(--secondary-color);
    }

    .feature-card {
        background: white;
        border-radius: 1.5rem;
        padding: 2.5rem 2rem;
        text-align: center;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-lg);
    }

    .feature-card:hover::before {
        transform: scaleX(1);
    }

    .feature-icon {
        width: 5rem;
        height: 5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border-radius: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        color: white;
        font-size: 2rem;
    }

    .feature-title {
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 1rem;
    }

    .feature-description {
        color: var(--text-muted);
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .feature-tech {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .tech-badge {
        background: var(--light-color);
        color: var(--primary-color);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
        border: 1px solid var(--border-color);
    }
</style>

<!-- Quick Actions Section -->
<section class="quick-actions-section py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-3">Bắt đầu quản lý</h2>
            <p class="lead text-muted">Truy cập nhanh các chức năng chính của hệ thống</p>
        </div>

        <div class="row g-4">
            <div class="col-md-6">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="action-content">
                        <h4>Quản lý sản phẩm</h4>
                        <p>Thêm, sửa, xóa và quản lý toàn bộ sản phẩm trong hệ thống</p>
                        <a href="/webbanhang/Product" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-2"></i>Truy cập ngay
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="action-content">
                        <h4>Quản lý danh mục</h4>
                        <p>Tổ chức và phân loại sản phẩm theo các danh mục khác nhau</p>
                        <a href="/webbanhang/Category" class="btn btn-success">
                            <i class="fas fa-arrow-right me-2"></i>Truy cập ngay
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4 mt-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h5>API Testing</h5>
                    <p>Test JWT authentication và API endpoints</p>
                    <a href="/webbanhang/final_jwt_test.html" class="btn btn-outline-primary btn-sm">
                        Test ngay
                    </a>
                </div>
            </div>

            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h5>UI Showcase</h5>
                    <p>Xem demo giao diện và tính năng</p>
                    <a href="/webbanhang/ui_showcase.html" class="btn btn-outline-success btn-sm">
                        Xem demo
                    </a>
                </div>
            </div>

            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h5>Logout Demo</h5>
                    <p>Test tính năng đăng xuất mới</p>
                    <a href="/webbanhang/logout_demo.html" class="btn btn-outline-warning btn-sm">
                        Test logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .quick-actions-section {
        background: white;
    }

    .action-card {
        background: linear-gradient(135deg, var(--light-color), #e2e8f0);
        border-radius: 1.5rem;
        padding: 2rem;
        display: flex;
        align-items: center;
        gap: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        height: 100%;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .action-icon {
        width: 5rem;
        height: 5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        border-radius: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        flex-shrink: 0;
    }

    .action-content h4 {
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .action-content p {
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }

    .stats-icon {
        width: 3rem;
        height: 3rem;
        background: var(--light-color);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: var(--primary-color);
        font-size: 1.2rem;
    }

    .stats-card h5 {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        color: var(--text-muted);
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .action-card {
            flex-direction: column;
            text-align: center;
        }

        .action-icon {
            margin-bottom: 1rem;
        }
    }
</style>



<?php include 'app/views/shares/footer.php'; ?>
