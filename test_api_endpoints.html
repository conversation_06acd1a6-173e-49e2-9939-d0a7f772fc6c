<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Endpoints</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        input, textarea { width: 100%; padding: 5px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>Test API Endpoints</h1>
    
    <div class="test-section">
        <h3>1. GET - L<PERSON>y danh sách sản phẩm</h3>
        <button onclick="testGetProducts()">Test GET /api/product</button>
        <div id="get-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. GET - Lấy sản phẩm theo ID</h3>
        <input type="number" id="product-id" placeholder="Product ID" value="1">
        <button onclick="testGetProduct()">Test GET /api/product/{id}</button>
        <div id="get-single-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. POST - Thêm sản phẩm mới</h3>
        <input type="text" id="new-name" placeholder="Tên sản phẩm" value="Test Product">
        <textarea id="new-description" placeholder="Mô tả">Test Description</textarea>
        <input type="number" id="new-price" placeholder="Giá" value="100000">
        <input type="number" id="new-category" placeholder="Category ID" value="1">
        <button onclick="testCreateProduct()">Test POST /api/product</button>
        <div id="post-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. PUT - Cập nhật sản phẩm</h3>
        <input type="number" id="update-id" placeholder="Product ID" value="1">
        <input type="text" id="update-name" placeholder="Tên sản phẩm" value="Updated Product">
        <textarea id="update-description" placeholder="Mô tả">Updated Description</textarea>
        <input type="number" id="update-price" placeholder="Giá" value="200000">
        <input type="number" id="update-category" placeholder="Category ID" value="1">
        <button onclick="testUpdateProduct()">Test PUT /api/product/{id}</button>
        <div id="put-result" class="result"></div>
    </div>

    <script>
        const baseUrl = window.location.origin + window.location.pathname.replace('test_api_endpoints.html', '');
        
        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.text();
                
                return {
                    status: response.status,
                    data: result,
                    ok: response.ok
                };
            } catch (error) {
                return {
                    status: 0,
                    data: error.message,
                    ok: false
                };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const className = result.ok ? 'success' : 'error';
            element.className = `result ${className}`;
            element.innerHTML = `
                <strong>Status:</strong> ${result.status}<br>
                <strong>Response:</strong><br>
                <pre>${result.data}</pre>
            `;
        }
        
        async function testGetProducts() {
            const result = await makeRequest(`${baseUrl}api/product`);
            displayResult('get-result', result);
        }
        
        async function testGetProduct() {
            const id = document.getElementById('product-id').value;
            const result = await makeRequest(`${baseUrl}api/product/${id}`);
            displayResult('get-single-result', result);
        }
        
        async function testCreateProduct() {
            const data = {
                name: document.getElementById('new-name').value,
                description: document.getElementById('new-description').value,
                price: document.getElementById('new-price').value,
                category_id: document.getElementById('new-category').value
            };
            const result = await makeRequest(`${baseUrl}api/product`, 'POST', data);
            displayResult('post-result', result);
        }
        
        async function testUpdateProduct() {
            const id = document.getElementById('update-id').value;
            const data = {
                name: document.getElementById('update-name').value,
                description: document.getElementById('update-description').value,
                price: document.getElementById('update-price').value,
                category_id: document.getElementById('update-category').value
            };
            const result = await makeRequest(`${baseUrl}api/product/${id}`, 'PUT', data);
            displayResult('put-result', result);
        }
    </script>
</body>
</html>
