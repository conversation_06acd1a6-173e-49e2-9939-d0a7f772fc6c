<?php
// Debug test file
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Test</h1>";

// Test 1: Database connection
echo "<h2>1. Database Connection</h2>";
try {
    require_once 'app/config/database.php';
    $db = (new Database())->getConnection();
    echo "✅ Database connection: OK<br>";
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

// Test 2: JWT Handler
echo "<h2>2. JWT Handler</h2>";
try {
    require_once 'app/utils/JWTHandler.php';
    $jwtHandler = new JWTHandler();
    $testToken = $jwtHandler->encode(['test' => 'data']);
    $decoded = $jwtHandler->decode($testToken);
    echo "✅ JWT Handler: OK<br>";
    echo "Test token: " . substr($testToken, 0, 50) . "...<br>";
} catch (Exception $e) {
    echo "❌ JWT Handler error: " . $e->getMessage() . "<br>";
}

// Test 3: JWT Middleware
echo "<h2>3. JWT Middleware</h2>";
try {
    require_once 'app/middleware/JWTMiddleware.php';
    $middleware = new JWTMiddleware();
    echo "✅ JWT Middleware: OK<br>";
} catch (Exception $e) {
    echo "❌ JWT Middleware error: " . $e->getMessage() . "<br>";
}

// Test 4: Account Model
echo "<h2>4. Account Model</h2>";
try {
    require_once 'app/models/AccountModel.php';
    $accountModel = new AccountModel($db);
    $admin = $accountModel->getAccountByUsername('admin');
    if ($admin) {
        echo "✅ Admin account found: " . $admin->username . " (role: " . $admin->role . ")<br>";
    } else {
        echo "❌ Admin account not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Account Model error: " . $e->getMessage() . "<br>";
}

// Test 5: Session Helper
echo "<h2>5. Session Helper</h2>";
try {
    require_once 'app/helpers/SessionHelper.php';
    echo "✅ Session Helper: OK<br>";
    echo "Session status: " . session_status() . "<br>";
} catch (Exception $e) {
    echo "❌ Session Helper error: " . $e->getMessage() . "<br>";
}

// Test 6: Product Controller
echo "<h2>6. Product Controller</h2>";
try {
    require_once 'app/controllers/ProductController.php';
    $controller = new ProductController();
    echo "✅ Product Controller: OK<br>";
} catch (Exception $e) {
    echo "❌ Product Controller error: " . $e->getMessage() . "<br>";
}

// Test 7: Account Controller
echo "<h2>7. Account Controller</h2>";
try {
    require_once 'app/controllers/AccountController.php';
    $accountController = new AccountController();
    echo "✅ Account Controller: OK<br>";
} catch (Exception $e) {
    echo "❌ Account Controller error: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>If you see any ❌ errors above, please share them with me.</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; border-bottom: 1px solid #ccc; }
</style>
