<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - WEBBANHANG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 API Test - WEBBANHANG</h1>
        
        <!-- Login Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🔑 Login API</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Admin Login</h6>
                        <button class="btn btn-primary" onclick="loginAs('admin', 'admin123')">Login as Admin</button>
                    </div>
                    <div class="col-md-6">
                        <h6>User Login</h6>
                        <button class="btn btn-secondary" onclick="loginAs('user', 'admin123')">Login as User</button>
                    </div>
                </div>
                <div id="loginResult" class="mt-3"></div>
            </div>
        </div>

        <!-- API Test Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🧪 Product API Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="testAPI('GET', '/api/product')">GET Products</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProduct()">POST Product</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="testUpdateProduct()">PUT Product</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-danger w-100 mb-2" onclick="testDeleteProduct()">DELETE Product</button>
                    </div>
                </div>
                <div id="apiResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Category API Test Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📁 Category API Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="testAPI('GET', '/api/category')">GET Categories</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateCategory()">POST Category</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="testUpdateCategory()">PUT Category</button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-danger w-100 mb-2" onclick="testDeleteCategory()">DELETE Category</button>
                    </div>
                </div>
                <div id="categoryApiResult" class="mt-3"></div>
            </div>
        </div>

        <!-- Token Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>🎫 Token Info</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-outline-primary" onclick="showTokenInfo()">Show Token</button>
                <button class="btn btn-outline-danger" onclick="clearToken()">Clear Token</button>
                <div id="tokenInfo" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
        // Decode JWT token
        function decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (error) {
                return null;
            }
        }

        // Login function
        async function loginAs(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    const decoded = decodeJWT(data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">
                            ✅ Login successful as <strong>${username}</strong><br>
                            Role: <strong>${decoded.data.role}</strong>
                        </div>`;
                    showTokenInfo();
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Generic API test
        async function testAPI(method, endpoint, body = null) {
            const token = localStorage.getItem('jwtToken');
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            try {
                const options = {
                    method: method,
                    headers: headers
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(`/webbanhang${endpoint}`, options);
                const data = await response.json();
                
                const statusClass = response.ok ? 'success' : 'danger';
                document.getElementById('apiResult').innerHTML = 
                    `<div class="alert alert-${statusClass}">
                        <strong>${method} ${endpoint}</strong><br>
                        Status: ${response.status}<br>
                        Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
            } catch (error) {
                document.getElementById('apiResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Test create product
        function testCreateProduct() {
            const productData = {
                name: 'Test Product API',
                description: 'Created via API test',
                price: 99000,
                category_id: 1
            };
            testAPI('POST', '/api/product', productData);
        }

        // Test update product
        function testUpdateProduct() {
            const productData = {
                name: 'Updated Test Product',
                description: 'Updated via API test',
                price: 199000,
                category_id: 1
            };
            testAPI('PUT', '/api/product/1', productData);
        }

        // Test delete product
        function testDeleteProduct() {
            testAPI('DELETE', '/api/product/1');
        }

        // Test create category
        function testCreateCategory() {
            const categoryData = {
                name: 'Test Category API',
                description: 'Created via API test'
            };
            testCategoryAPI('POST', '/api/category', categoryData);
        }

        // Test update category
        function testUpdateCategory() {
            const categoryData = {
                name: 'Updated Test Category',
                description: 'Updated via API test'
            };
            testCategoryAPI('PUT', '/api/category/1', categoryData);
        }

        // Test delete category
        function testDeleteCategory() {
            testCategoryAPI('DELETE', '/api/category/1');
        }

        // Generic Category API test
        async function testCategoryAPI(method, endpoint, body = null) {
            const token = localStorage.getItem('jwtToken');
            const headers = {
                'Content-Type': 'application/json'
            };

            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            try {
                const options = {
                    method: method,
                    headers: headers
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(`/webbanhang${endpoint}`, options);
                const data = await response.json();

                const statusClass = response.ok ? 'success' : 'danger';
                document.getElementById('categoryApiResult').innerHTML =
                    `<div class="alert alert-${statusClass}">
                        <strong>${method} ${endpoint}</strong><br>
                        Status: ${response.status}<br>
                        Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
            } catch (error) {
                document.getElementById('categoryApiResult').innerHTML =
                    `<div class="alert alert-danger">❌ Error: ${error.message}</div>`;
            }
        }

        // Show token info
        function showTokenInfo() {
            const token = localStorage.getItem('jwtToken');
            if (token) {
                const decoded = decodeJWT(token);
                document.getElementById('tokenInfo').innerHTML = 
                    `<div class="alert alert-info">
                        <strong>Token Info:</strong>
                        <pre>${JSON.stringify(decoded, null, 2)}</pre>
                    </div>`;
            } else {
                document.getElementById('tokenInfo').innerHTML = 
                    '<div class="alert alert-warning">No token found</div>';
            }
        }

        // Clear token
        function clearToken() {
            localStorage.removeItem('jwtToken');
            document.getElementById('tokenInfo').innerHTML = 
                '<div class="alert alert-info">Token cleared</div>';
            document.getElementById('loginResult').innerHTML = '';
        }

        // Show token info on load
        showTokenInfo();
    </script>
</body>
</html>
