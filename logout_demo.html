<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout Demo - Web B<PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --dark-color: #1e293b;
            --light-color: #f1f5f9;
            --border-color: #e2e8f0;
            --text-muted: #64748b;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark-color);
        }

        .demo-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .demo-button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
            color: white;
        }

        .logout-button {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .logout-button:hover {
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: var(--accent-color);
            width: 20px;
        }

        .code-block {
            background: var(--dark-color);
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-sign-out-alt me-3" style="color: var(--danger-color);"></i>
                Logout Demo
            </h1>
            <p class="lead text-muted">Demo tính năng đăng xuất mới với modal xác nhận và UX cải tiến</p>
        </div>

        <!-- Quick Login -->
        <div class="demo-card">
            <h3 class="mb-4">
                <i class="fas fa-sign-in-alt me-2" style="color: var(--primary-color);"></i>
                Đăng nhập nhanh
            </h3>
            <p class="text-muted mb-4">Đăng nhập để test tính năng logout mới</p>
            <div class="text-center">
                <button class="demo-button" onclick="quickLogin('admin', 'admin123')">
                    <i class="fas fa-crown me-2"></i>Login as Admin
                </button>
                <button class="demo-button" onclick="quickLogin('user', 'admin123')">
                    <i class="fas fa-user me-2"></i>Login as User
                </button>
            </div>
            <div id="loginResult" class="mt-3"></div>
        </div>

        <!-- Logout Features -->
        <div class="demo-card">
            <h3 class="mb-4">
                <i class="fas fa-star me-2" style="color: var(--warning-color);"></i>
                Tính năng mới của Logout
            </h3>
            <div class="row">
                <div class="col-md-6">
                    <ul class="feature-list">
                        <li><i class="fas fa-check me-2"></i>Modal xác nhận trước khi đăng xuất</li>
                        <li><i class="fas fa-check me-2"></i>Loading state với spinner animation</li>
                        <li><i class="fas fa-check me-2"></i>Toast notification thông báo thành công</li>
                        <li><i class="fas fa-check me-2"></i>Smooth transition và redirect</li>
                        <li><i class="fas fa-check me-2"></i>Dropdown menu với nhiều options</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="feature-list">
                        <li><i class="fas fa-check me-2"></i>Clear JWT token khỏi localStorage</li>
                        <li><i class="fas fa-check me-2"></i>Responsive design cho mobile</li>
                        <li><i class="fas fa-check me-2"></i>Hover effects và animations</li>
                        <li><i class="fas fa-check me-2"></i>Consistent với design system</li>
                        <li><i class="fas fa-check me-2"></i>Accessibility friendly</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Logout -->
        <div class="demo-card">
            <h3 class="mb-4">
                <i class="fas fa-flask me-2" style="color: var(--accent-color);"></i>
                Test Logout
            </h3>
            <p class="text-muted mb-4">Sau khi đăng nhập, bạn có thể test logout bằng các cách sau:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Cách 1: Từ Navigation</h5>
                    <ol>
                        <li>Click vào avatar/username ở góc phải</li>
                        <li>Click "Đăng xuất" trong dropdown</li>
                        <li>Xác nhận trong modal popup</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h5>Cách 2: Direct Link</h5>
                    <div class="text-center">
                        <button class="demo-button logout-button" onclick="testLogout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Test Logout Modal
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Example -->
        <div class="demo-card">
            <h3 class="mb-4">
                <i class="fas fa-code me-2" style="color: var(--primary-color);"></i>
                Code Implementation
            </h3>
            <p class="text-muted mb-3">JavaScript code cho logout functionality:</p>
            <div class="code-block">
<pre>// Show logout confirmation modal
function confirmLogout(event) {
    event.preventDefault();
    const logoutModal = new bootstrap.Modal(document.getElementById('logoutModal'));
    logoutModal.show();
}

// Perform actual logout
function performLogout() {
    const logoutBtn = document.querySelector('.btn-logout');
    const btnText = logoutBtn.querySelector('.logout-btn-text');
    const btnLoading = logoutBtn.querySelector('.logout-btn-loading');
    
    // Show loading state
    btnText.classList.add('d-none');
    btnLoading.classList.remove('d-none');
    logoutBtn.disabled = true;
    
    // Clear JWT token
    localStorage.removeItem('jwtToken');
    
    // Show success message
    showToast('Đăng xuất thành công!', 'success');
    
    // Smooth redirect after delay
    setTimeout(() => {
        window.location.href = '/webbanhang/account/logout';
    }, 1000);
}</pre>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="demo-card text-center">
            <h3 class="mb-4">
                <i class="fas fa-link me-2" style="color: var(--accent-color);"></i>
                Navigation
            </h3>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                <a href="/webbanhang/account/login" class="demo-button">
                    <i class="fas fa-sign-in-alt me-2"></i>Login Page
                </a>
                <a href="/webbanhang/Product" class="demo-button">
                    <i class="fas fa-box me-2"></i>Product Management
                </a>
                <a href="/webbanhang/ui_showcase.html" class="demo-button">
                    <i class="fas fa-palette me-2"></i>UI Showcase
                </a>
                <a href="/webbanhang/final_jwt_test.html" class="demo-button">
                    <i class="fas fa-flask me-2"></i>API Test
                </a>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Quick login function
        async function quickLogin(username, password) {
            try {
                const response = await fetch('/webbanhang/account/checkLogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-success">
                            ✅ Đăng nhập thành công! Bây giờ bạn có thể test logout.
                            <br><a href="/webbanhang/Product" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-arrow-right me-1"></i>Đi đến Product Management
                            </a>
                        </div>`;
                    showToast(`Đăng nhập thành công với tài khoản ${username}!`, 'success');
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<div class="alert alert-danger">❌ Đăng nhập thất bại</div>`;
                    showToast('Đăng nhập thất bại!', 'danger');
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-danger">❌ Lỗi: ${error.message}</div>`;
                showToast('Có lỗi xảy ra!', 'danger');
            }
        }

        // Test logout modal
        function testLogout() {
            const token = localStorage.getItem('jwtToken');
            if (!token) {
                showToast('Vui lòng đăng nhập trước để test logout!', 'warning');
                return;
            }
            
            // Simulate the logout modal (since we're not in the main app)
            if (confirm('Bạn có chắc chắn muốn đăng xuất?\n\n(Đây là demo - trong app thực sẽ có modal đẹp hơn)')) {
                localStorage.removeItem('jwtToken');
                showToast('Đăng xuất thành công! (Demo)', 'success');
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Đã đăng xuất. Hãy đăng nhập lại để test.
                    </div>`;
            }
        }

        // Enhanced Toast function
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();
            
            const iconMap = {
                'success': 'fas fa-check-circle',
                'danger': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            
            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast align-items-center text-white bg-${type} border-0 shadow-lg`;
            toast.style.borderRadius = '0.75rem';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body d-flex align-items-center">
                        <i class="${iconMap[type] || 'fas fa-info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 4000
            });
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }

        // Check login status on page load
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('jwtToken');
            if (token) {
                document.getElementById('loginResult').innerHTML = 
                    `<div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Bạn đã đăng nhập. Có thể test logout ngay.
                    </div>`;
            }
        });
    </script>
</body>
</html>
