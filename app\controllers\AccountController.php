<?php

require_once('app/config/database.php'); 
require_once('app/models/AccountModel.php'); 
require_once('app/utils/JWTHandler.php'); 

class AccountController 
{
    private $accountModel;
    private $db;
    private $jwtHandler;

    public function __construct() 
    {
        $this->db = (new Database())->getConnection();
        $this->accountModel = new AccountModel($this->db);
        $this->jwtHandler = new JWTHandler();
    }

    public function register()
    {
        include_once 'app/views/account/register.php';
    }

    public function login() 
    {
        include_once 'app/views/account/login.php';
    }

    public function save()
    {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            return;
        }

        $username = $_POST['username'] ?? '';
        $fullName = $_POST['fullname'] ?? '';
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirmpassword'] ?? '';

        $errors = [];
        
        // Validate input
        if(empty($username)) {
            $errors['username'] = "Vui lòng nhập userName!";
        }
        if(empty($fullName)) {
            $errors['fullname'] = "Vui lòng nhập fullName!";
        }
        if(empty($password)) {
            $errors['password'] = "Vui lòng nhập password!";
        }
        if($password != $confirmPassword) {
            $errors['confirmPass'] = "Mật khẩu và xác nhận chưa đúng";
        }

        // Check existing username
        $account = $this->accountModel->getAccountByUsername($username);
        if($account) {
            $errors['account'] = "Tài khoản này đã có người đăng ký!";
        }

        if(count($errors) > 0) {
            include_once 'app/views/account/register.php';
            return;
        }

        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        $result = $this->accountModel->save($username, $fullName, $hashedPassword);
        
        if($result) {
            header('Location: /webbanhang/account/login');
        }
    }

    // Đăng nhập (tạo cả Session và JWT)
    public function checkLogin()
    {
        // Kiểm tra xem là API call hay form submit
        $isApiCall = $_SERVER['CONTENT_TYPE'] === 'application/json' ||
                     strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false;

        if ($isApiCall) {
            // API call - trả về JSON
            header('Content-Type: application/json');
            $data = json_decode(file_get_contents("php://input"), true);
            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';
        } else {
            // Form submit - xử lý POST data
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
        }

        $user = $this->accountModel->getAccountByUsername($username);

        if ($user && password_verify($password, $user->password)) {
            // Tạo session cho cả hai trường hợp
            SessionHelper::start();
            $_SESSION['username'] = $user->username;
            $_SESSION['role'] = $user->role;
            $_SESSION['user_id'] = $user->id;

            if ($isApiCall) {
                // Trả về JWT token cho API
                $token = $this->jwtHandler->encode([
                    'id' => $user->id,
                    'username' => $user->username,
                    'role' => $user->role
                ]);
                echo json_encode(['token' => $token]);
            } else {
                // Chuyển hướng cho web
                header('Location: /webbanhang/Product');
            }
        } else {
            if ($isApiCall) {
                http_response_code(401);
                echo json_encode(['message' => 'Invalid credentials']);
            } else {
                $error = "Tên đăng nhập hoặc mật khẩu không đúng!";
                include_once 'app/views/account/login.php';
            }
        }
    }

    // Đăng xuất
    public function logout()
    {
        SessionHelper::start();
        unset($_SESSION['username']);
        unset($_SESSION['role']);
        unset($_SESSION['user_id']);
        session_destroy();
        header('Location: /webbanhang/account/login');
    }


}