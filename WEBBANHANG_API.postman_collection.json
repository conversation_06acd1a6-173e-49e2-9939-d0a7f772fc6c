{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "WEBBANHANG API", "description": "API Collection for WEBBANHANG project", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/product", "host": ["{{base_url}}"], "path": ["api", "product"]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/product/1", "host": ["{{base_url}}"], "path": ["api", "product", "1"]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Product\",\n    \"description\": \"Test Description\",\n    \"price\": 50000,\n    \"category_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/product", "host": ["{{base_url}}"], "path": ["api", "product"]}}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Product\",\n    \"description\": \"Updated Description\",\n    \"price\": 75000,\n    \"category_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/product/1", "host": ["{{base_url}}"], "path": ["api", "product", "1"]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/product/1", "host": ["{{base_url}}"], "path": ["api", "product", "1"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/category", "host": ["{{base_url}}"], "path": ["api", "category"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/category/1", "host": ["{{base_url}}"], "path": ["api", "category", "1"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost/WEBBANHANG"}]}